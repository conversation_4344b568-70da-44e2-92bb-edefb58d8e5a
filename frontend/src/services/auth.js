import { apiMethods } from './api'

export const authService = {
  // Login user
  async login(credentials) {
    const response = await apiMethods.post('/auth/login', credentials)
    return response.data.data
  },

  // Register user
  async register(userData) {
    const response = await apiMethods.post('/auth/register', userData)
    return response.data.data
  },

  // Logout user
  async logout() {
    try {
      await apiMethods.post('/auth/logout')
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', error)
    }
  },

  // Get current user
  async getCurrentUser() {
    const response = await apiMethods.get('/users/me')
    return response.data.data
  },

  // Update user profile
  async updateProfile(profileData) {
    const response = await apiMethods.put('/users/me', profileData)
    return response.data.data
  },

  // Change password
  async changePassword(passwordData) {
    const response = await apiMethods.put('/auth/change-password', passwordData)
    return response.data
  },

  // Delete account
  async deleteAccount() {
    const response = await apiMethods.delete('/users/me')
    return response.data
  },

  // Refresh token
  async refreshToken(refreshToken) {
    const response = await apiMethods.post('/auth/refresh', { refreshToken })
    return response.data.data
  },

  // Forgot password
  async forgotPassword(email) {
    const response = await apiMethods.post('/auth/forgot-password', { email })
    return response.data
  },

  // Reset password
  async resetPassword(token, newPassword) {
    const response = await apiMethods.post('/auth/reset-password', {
      token,
      password: newPassword,
    })
    return response.data
  },

  // Verify email
  async verifyEmail(token) {
    const response = await apiMethods.post('/auth/verify-email', { token })
    return response.data
  },

  // Resend verification email
  async resendVerification(email) {
    const response = await apiMethods.post('/auth/resend-verification', { email })
    return response.data
  },

  // Check username availability
  async checkUsername(username) {
    const response = await apiMethods.get(`/auth/check-username/${username}`)
    return response.data.data
  },

  // Check email availability
  async checkEmail(email) {
    const response = await apiMethods.get(`/auth/check-email/${email}`)
    return response.data.data
  },

  // Get user by ID
  async getUserById(userId) {
    const response = await apiMethods.get(`/users/${userId}`)
    return response.data.data
  },

  // Update user avatar
  async updateAvatar(file) {
    const formData = new FormData()
    formData.append('avatar', file)
    
    const response = await apiMethods.upload('/users/me/avatar', formData)
    return response.data.data
  },

  // Delete user avatar
  async deleteAvatar() {
    const response = await apiMethods.delete('/users/me/avatar')
    return response.data
  },

  // Get user stats
  async getUserStats() {
    const response = await apiMethods.get('/users/me/stats')
    return response.data.data
  },

  // Update user preferences
  async updatePreferences(preferences) {
    const response = await apiMethods.put('/users/me/preferences', preferences)
    return response.data.data
  },

  // Get user preferences
  async getPreferences() {
    const response = await apiMethods.get('/users/me/preferences')
    return response.data.data
  },

  // Block user
  async blockUser(userId) {
    const response = await apiMethods.post(`/users/${userId}/block`)
    return response.data
  },

  // Unblock user
  async unblockUser(userId) {
    const response = await apiMethods.delete(`/users/${userId}/block`)
    return response.data
  },

  // Report user
  async reportUser(userId, reason, description) {
    const response = await apiMethods.post(`/users/${userId}/report`, {
      reason,
      description,
    })
    return response.data
  },

  // Get blocked users
  async getBlockedUsers() {
    const response = await apiMethods.get('/users/me/blocked')
    return response.data.data
  },

  // Enable two-factor authentication
  async enableTwoFactor() {
    const response = await apiMethods.post('/auth/2fa/enable')
    return response.data.data
  },

  // Disable two-factor authentication
  async disableTwoFactor(code) {
    const response = await apiMethods.post('/auth/2fa/disable', { code })
    return response.data
  },

  // Verify two-factor authentication
  async verifyTwoFactor(code) {
    const response = await apiMethods.post('/auth/2fa/verify', { code })
    return response.data.data
  },

  // Get backup codes for 2FA
  async getBackupCodes() {
    const response = await apiMethods.get('/auth/2fa/backup-codes')
    return response.data.data
  },

  // Regenerate backup codes
  async regenerateBackupCodes() {
    const response = await apiMethods.post('/auth/2fa/backup-codes/regenerate')
    return response.data.data
  },
}
