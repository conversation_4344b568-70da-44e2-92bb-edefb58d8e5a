import React, { Suspense, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { motion, AnimatePresence } from 'framer-motion'

import { useAuth } from './hooks/useAuth'
import LoadingScreen from './components/common/LoadingScreen'
import ProtectedRoute from './components/auth/ProtectedRoute'
import Layout from './components/layout/Layout'

// Lazy load pages for better performance
const Home = React.lazy(() => import('./pages/Home'))
const Login = React.lazy(() => import('./pages/Login'))
const Register = React.lazy(() => import('./pages/Register'))
const Profile = React.lazy(() => import('./pages/Profile'))
const Messages = React.lazy(() => import('./pages/Messages'))
const Matches = React.lazy(() => import('./pages/Matches'))
const Settings = React.lazy(() => import('./pages/Settings'))
const Admin = React.lazy(() => import('./pages/Admin'))
const NotFound = React.lazy(() => import('./pages/NotFound'))

// Page transition variants
const pageVariants = {
  initial: {
    opacity: 0,
    x: 20,
  },
  in: {
    opacity: 1,
    x: 0,
  },
  out: {
    opacity: 0,
    x: -20,
  },
}

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3,
}

function App() {
  const { user, loading, checkAuth } = useAuth()

  useEffect(() => {
    // Check authentication status on app load
    checkAuth()
  }, [checkAuth])

  // Show loading screen while checking authentication
  if (loading) {
    return <LoadingScreen />
  }

  return (
    <>
      <Helmet>
        <title>SoloyLibre - Find Love Through Photos</title>
        <meta name="description" content="Discover meaningful connections through shared visual interests. SoloyLibre is a photo-based dating platform that connects hearts through beautiful imagery." />
        <meta name="keywords" content="dating, photos, love, relationships, social, matching" />
        <meta property="og:title" content="SoloyLibre - Find Love Through Photos" />
        <meta property="og:description" content="Discover meaningful connections through shared visual interests." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={window.location.href} />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="SoloyLibre - Find Love Through Photos" />
        <meta name="twitter:description" content="Discover meaningful connections through shared visual interests." />
      </Helmet>

      <div className="min-h-screen bg-black text-white">
        <AnimatePresence mode="wait">
          <Routes>
            {/* Public routes */}
            <Route
              path="/login"
              element={
                user ? (
                  <Navigate to="/" replace />
                ) : (
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <Suspense fallback={<LoadingScreen />}>
                      <Login />
                    </Suspense>
                  </motion.div>
                )
              }
            />
            
            <Route
              path="/register"
              element={
                user ? (
                  <Navigate to="/" replace />
                ) : (
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <Suspense fallback={<LoadingScreen />}>
                      <Register />
                    </Suspense>
                  </motion.div>
                )
              }
            />

            {/* Protected routes */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <Suspense fallback={<LoadingScreen />}>
                        <Home />
                      </Suspense>
                    </motion.div>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  <Layout>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <Suspense fallback={<LoadingScreen />}>
                        <Profile />
                      </Suspense>
                    </motion.div>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/messages"
              element={
                <ProtectedRoute>
                  <Layout>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <Suspense fallback={<LoadingScreen />}>
                        <Messages />
                      </Suspense>
                    </motion.div>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/matches"
              element={
                <ProtectedRoute>
                  <Layout>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <Suspense fallback={<LoadingScreen />}>
                        <Matches />
                      </Suspense>
                    </motion.div>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/settings"
              element={
                <ProtectedRoute>
                  <Layout>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <Suspense fallback={<LoadingScreen />}>
                        <Settings />
                      </Suspense>
                    </motion.div>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin"
              element={
                <ProtectedRoute requireAdmin>
                  <Layout>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <Suspense fallback={<LoadingScreen />}>
                        <Admin />
                      </Suspense>
                    </motion.div>
                  </Layout>
                </ProtectedRoute>
              }
            />

            {/* 404 route */}
            <Route
              path="*"
              element={
                <motion.div
                  initial="initial"
                  animate="in"
                  exit="out"
                  variants={pageVariants}
                  transition={pageTransition}
                >
                  <Suspense fallback={<LoadingScreen />}>
                    <NotFound />
                  </Suspense>
                </motion.div>
              }
            />
          </Routes>
        </AnimatePresence>
      </div>
    </>
  )
}

export default App
