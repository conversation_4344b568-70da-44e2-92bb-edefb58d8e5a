import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react'

// Initial state
const initialState = {
  theme: 'dark', // Always dark for our app
  systemPreference: 'dark',
  accentColor: 'primary', // primary, blue, green, purple, orange
  animations: true,
  reducedMotion: false,
}

// Action types
const THEME_ACTIONS = {
  SET_THEME: 'SET_THEME',
  SET_ACCENT_COLOR: 'SET_ACCENT_COLOR',
  SET_ANIMATIONS: 'SET_ANIMATIONS',
  SET_REDUCED_MOTION: 'SET_REDUCED_MOTION',
  SET_SYSTEM_PREFERENCE: 'SET_SYSTEM_PREFERENCE',
}

// Reducer
function themeReducer(state, action) {
  switch (action.type) {
    case THEME_ACTIONS.SET_THEME:
      return {
        ...state,
        theme: action.payload,
      }
    
    case THEME_ACTIONS.SET_ACCENT_COLOR:
      return {
        ...state,
        accentColor: action.payload,
      }
    
    case THEME_ACTIONS.SET_ANIMATIONS:
      return {
        ...state,
        animations: action.payload,
      }
    
    case THEME_ACTIONS.SET_REDUCED_MOTION:
      return {
        ...state,
        reducedMotion: action.payload,
      }
    
    case THEME_ACTIONS.SET_SYSTEM_PREFERENCE:
      return {
        ...state,
        systemPreference: action.payload,
      }
    
    default:
      return state
  }
}

// Accent color configurations
const accentColors = {
  primary: {
    50: '#fff1f2',
    100: '#ffe4e6',
    200: '#fecdd3',
    300: '#fda4af',
    400: '#fb7185',
    500: '#f43f5e',
    600: '#e11d48',
    700: '#be123c',
    800: '#9f1239',
    900: '#881337',
    950: '#4c0519',
  },
  blue: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },
  green: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },
  purple: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7',
    600: '#9333ea',
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87',
    950: '#3b0764',
  },
  orange: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316',
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
    950: '#431407',
  },
}

// Create context
const ThemeContext = createContext()

// Provider component
export function ThemeProvider({ children }) {
  const [state, dispatch] = useReducer(themeReducer, () => {
    // Load saved preferences from localStorage
    const savedTheme = localStorage.getItem('theme') || 'dark'
    const savedAccentColor = localStorage.getItem('accentColor') || 'primary'
    const savedAnimations = localStorage.getItem('animations') !== 'false'
    
    return {
      ...initialState,
      theme: savedTheme,
      accentColor: savedAccentColor,
      animations: savedAnimations,
    }
  })

  // Set theme
  const setTheme = useCallback((theme) => {
    dispatch({ type: THEME_ACTIONS.SET_THEME, payload: theme })
    localStorage.setItem('theme', theme)
    
    // Update document class
    document.documentElement.className = theme
  }, [])

  // Set accent color
  const setAccentColor = useCallback((color) => {
    dispatch({ type: THEME_ACTIONS.SET_ACCENT_COLOR, payload: color })
    localStorage.setItem('accentColor', color)
    
    // Update CSS custom properties
    const colorPalette = accentColors[color]
    if (colorPalette) {
      Object.entries(colorPalette).forEach(([shade, value]) => {
        document.documentElement.style.setProperty(`--color-accent-${shade}`, value)
      })
    }
  }, [])

  // Set animations
  const setAnimations = useCallback((enabled) => {
    dispatch({ type: THEME_ACTIONS.SET_ANIMATIONS, payload: enabled })
    localStorage.setItem('animations', enabled.toString())
    
    // Update document class for animations
    if (enabled) {
      document.documentElement.classList.remove('no-animations')
    } else {
      document.documentElement.classList.add('no-animations')
    }
  }, [])

  // Detect system preferences
  useEffect(() => {
    // Detect system color scheme preference
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = (e) => {
      dispatch({
        type: THEME_ACTIONS.SET_SYSTEM_PREFERENCE,
        payload: e.matches ? 'dark' : 'light',
      })
    }
    
    mediaQuery.addEventListener('change', handleChange)
    handleChange(mediaQuery) // Set initial value
    
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Detect reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    const handleChange = (e) => {
      dispatch({
        type: THEME_ACTIONS.SET_REDUCED_MOTION,
        payload: e.matches,
      })
      
      // Automatically disable animations if user prefers reduced motion
      if (e.matches) {
        setAnimations(false)
      }
    }
    
    mediaQuery.addEventListener('change', handleChange)
    handleChange(mediaQuery) // Set initial value
    
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [setAnimations])

  // Apply theme on mount and changes
  useEffect(() => {
    document.documentElement.className = state.theme
    setAccentColor(state.accentColor)
    setAnimations(state.animations)
  }, [state.theme, state.accentColor, state.animations, setAccentColor, setAnimations])

  // Get current accent color palette
  const getCurrentAccentColor = useCallback(() => {
    return accentColors[state.accentColor] || accentColors.primary
  }, [state.accentColor])

  // Context value
  const value = {
    ...state,
    setTheme,
    setAccentColor,
    setAnimations,
    getCurrentAccentColor,
    accentColors,
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

// Hook to use theme context
export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

export default ThemeContext
