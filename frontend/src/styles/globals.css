@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables for Dark Theme */
:root {
  --color-primary: #ff6b6b;
  --color-primary-dark: #e11d48;
  --color-secondary: #f97316;
  --color-background: #000000;
  --color-surface: #1a1a1a;
  --color-surface-elevated: #2d2d2d;
  --color-text-primary: #ffffff;
  --color-text-secondary: #a1a1aa;
  --color-text-tertiary: #71717a;
  --color-border: rgba(255, 255, 255, 0.1);
  --color-border-focus: rgba(255, 107, 107, 0.5);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --blur-sm: 4px;
  --blur: 8px;
  --blur-lg: 16px;
  --blur-xl: 24px;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* iOS-like scrollbar */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* Selection styles */
::selection {
  background-color: rgba(255, 107, 107, 0.3);
  color: white;
}

::-moz-selection {
  background-color: rgba(255, 107, 107, 0.3);
  color: white;
}

/* Focus styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

/* Button base styles */
button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
  font-size: inherit;
  transition: all 0.2s ease;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Input base styles */
input, textarea, select {
  font-family: inherit;
  font-size: 16px; /* Prevent zoom on iOS */
  border: none;
  background: transparent;
  color: inherit;
}

input::placeholder,
textarea::placeholder {
  color: var(--color-text-tertiary);
}

/* Link styles */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primary-dark);
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Custom utility classes */
.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-morphism-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-text {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-border {
  position: relative;
  background: var(--color-surface);
  border-radius: 12px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
}

/* iOS-like button styles */
.btn-primary {
  @apply bg-gradient-primary text-white font-semibold py-3 px-6 rounded-ios-lg shadow-glow;
  @apply hover:shadow-glow-lg active:scale-95 transition-all duration-200;
}

.btn-secondary {
  @apply bg-dark-800 text-white font-semibold py-3 px-6 rounded-ios-lg border border-dark-600;
  @apply hover:bg-dark-700 active:scale-95 transition-all duration-200;
}

.btn-ghost {
  @apply text-white font-semibold py-3 px-6 rounded-ios-lg;
  @apply hover:bg-white/10 active:scale-95 transition-all duration-200;
}

/* iOS-like input styles */
.input-field {
  @apply w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-ios-lg;
  @apply text-white placeholder-gray-400 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20;
  @apply transition-all duration-200;
}

/* Card styles */
.card {
  @apply bg-dark-800 rounded-ios-lg border border-dark-600 shadow-card;
  @apply hover:shadow-card-hover transition-all duration-300;
}

.card-elevated {
  @apply bg-gradient-card rounded-ios-lg border border-dark-600 shadow-card;
  @apply hover:shadow-card-hover transition-all duration-300;
}

/* Photo card styles */
.photo-card {
  @apply relative overflow-hidden rounded-ios-xl shadow-card;
  @apply hover:shadow-card-hover transition-all duration-300;
  aspect-ratio: 3/4;
}

.photo-card img {
  @apply w-full h-full object-cover;
}

.photo-card-overlay {
  @apply absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent;
  @apply opacity-0 hover:opacity-100 transition-opacity duration-300;
}

/* Swipe animations */
.swipe-card {
  @apply relative bg-dark-800 rounded-ios-xl overflow-hidden shadow-card;
  @apply cursor-grab active:cursor-grabbing;
  aspect-ratio: 3/4;
  transform-origin: center;
}

.swipe-card.dragging {
  @apply z-10 shadow-card-hover;
}

.swipe-card.swiped-right {
  animation: swipeRight 0.3s ease-out forwards;
}

.swipe-card.swiped-left {
  animation: swipeLeft 0.3s ease-out forwards;
}

@keyframes swipeRight {
  to {
    transform: translateX(100vw) rotate(30deg);
    opacity: 0;
  }
}

@keyframes swipeLeft {
  to {
    transform: translateX(-100vw) rotate(-30deg);
    opacity: 0;
  }
}

/* Loading animations */
.loading-spinner {
  @apply w-6 h-6 border-2 border-gray-600 border-t-primary-500 rounded-full;
  animation: spin 1s linear infinite;
}

.loading-dots {
  @apply flex space-x-1;
}

.loading-dots div {
  @apply w-2 h-2 bg-primary-500 rounded-full;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots div:nth-child(1) { animation-delay: -0.32s; }
.loading-dots div:nth-child(2) { animation-delay: -0.16s; }

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Pulse animation for skeleton loading */
.skeleton {
  @apply bg-dark-700 rounded animate-pulse;
}

/* iOS-like navigation */
.nav-tab {
  @apply flex flex-col items-center justify-center p-2 text-gray-400;
  @apply hover:text-white transition-colors duration-200;
}

.nav-tab.active {
  @apply text-primary-500;
}

.nav-tab-indicator {
  @apply w-1 h-1 bg-primary-500 rounded-full mt-1 opacity-0;
  @apply transition-opacity duration-200;
}

.nav-tab.active .nav-tab-indicator {
  @apply opacity-100;
}

/* Safe area support for iOS */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Responsive design helpers */
@media (max-width: 640px) {
  .mobile-full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
