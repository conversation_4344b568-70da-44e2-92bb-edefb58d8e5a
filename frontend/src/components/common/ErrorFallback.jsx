import React from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, Home, AlertTriangle } from 'lucide-react'

const ErrorFallback = ({ error, resetErrorBoundary }) => {
  const handleGoHome = () => {
    window.location.href = '/'
  }

  const handleReload = () => {
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <motion.div
        className="max-w-md w-full text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Error icon */}
        <motion.div
          className="w-20 h-20 bg-red-500/20 rounded-ios-lg mx-auto mb-6 flex items-center justify-center"
          animate={{
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <AlertTriangle className="w-10 h-10 text-red-500" />
        </motion.div>

        {/* Error message */}
        <h1 className="text-2xl font-bold text-white mb-2">
          Oops! Something went wrong
        </h1>
        
        <p className="text-gray-400 mb-8">
          We're sorry, but something unexpected happened. Don't worry, it's not your fault.
        </p>

        {/* Action buttons */}
        <div className="space-y-3">
          <motion.button
            onClick={resetErrorBoundary}
            className="w-full bg-gradient-primary text-white font-semibold py-3 px-6 rounded-ios-lg shadow-glow hover:shadow-glow-lg transition-all duration-200 flex items-center justify-center space-x-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <RefreshCw className="w-5 h-5" />
            <span>Try Again</span>
          </motion.button>

          <motion.button
            onClick={handleGoHome}
            className="w-full bg-dark-800 text-white font-semibold py-3 px-6 rounded-ios-lg border border-dark-600 hover:bg-dark-700 transition-all duration-200 flex items-center justify-center space-x-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Home className="w-5 h-5" />
            <span>Go Home</span>
          </motion.button>

          <motion.button
            onClick={handleReload}
            className="w-full text-gray-400 font-medium py-2 px-4 rounded-ios hover:text-white transition-colors duration-200"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            Reload Page
          </motion.button>
        </div>

        {/* Error details for development */}
        {import.meta.env.DEV && error && (
          <motion.details
            className="mt-8 text-left"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <summary className="text-gray-400 cursor-pointer mb-3 text-sm">
              Error Details (Development)
            </summary>
            <div className="bg-dark-900 border border-dark-700 rounded-ios p-4">
              <pre className="text-xs text-red-400 overflow-auto whitespace-pre-wrap">
                {error.message}
                {error.stack && (
                  <>
                    {'\n\nStack Trace:\n'}
                    {error.stack}
                  </>
                )}
              </pre>
            </div>
          </motion.details>
        )}

        {/* Help text */}
        <motion.p
          className="text-xs text-gray-500 mt-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          If this problem persists, please contact our support team.
        </motion.p>
      </motion.div>
    </div>
  )
}

export default ErrorFallback
