import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from 'react-query'
import { ReactQueryDevtools } from 'react-query/devtools'
import { <PERSON><PERSON><PERSON>Provider } from 'react-helmet-async'
import { ErrorBoundary } from 'react-error-boundary'
import { Toaster } from 'react-hot-toast'

import App from './App.jsx'
import { AuthProvider } from './context/AuthContext.jsx'
import { ThemeProvider } from './context/ThemeContext.jsx'
import ErrorFallback from './components/common/ErrorFallback.jsx'
import './styles/globals.css'

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
})

// Error boundary fallback component
function AppErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-6">
          <div className="w-20 h-20 bg-gradient-primary rounded-ios-lg mx-auto mb-4 flex items-center justify-center">
            <span className="text-2xl">💔</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">Oops! Something went wrong</h1>
          <p className="text-gray-400 mb-6">
            We're sorry, but something unexpected happened. Please try again.
          </p>
        </div>
        
        <button
          onClick={resetErrorBoundary}
          className="w-full bg-gradient-primary text-white font-semibold py-3 px-6 rounded-ios-lg shadow-glow hover:shadow-glow-lg transition-all duration-200"
        >
          Try Again
        </button>
        
        {import.meta.env.DEV && (
          <details className="mt-6 text-left">
            <summary className="text-gray-400 cursor-pointer mb-2">Error Details</summary>
            <pre className="text-xs text-red-400 bg-dark-900 p-3 rounded-ios overflow-auto">
              {error.message}
            </pre>
          </details>
        )}
      </div>
    </div>
  )
}

// Toast configuration
const toastOptions = {
  duration: 4000,
  position: 'top-center',
  style: {
    background: 'rgba(0, 0, 0, 0.8)',
    color: '#fff',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    borderRadius: '12px',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
  },
  success: {
    iconTheme: {
      primary: '#34C759',
      secondary: '#fff',
    },
  },
  error: {
    iconTheme: {
      primary: '#FF3B30',
      secondary: '#fff',
    },
  },
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ErrorBoundary
      FallbackComponent={AppErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Application Error:', error, errorInfo)
        // Here you could send error to monitoring service like Sentry
      }}
    >
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <BrowserRouter>
            <ThemeProvider>
              <AuthProvider>
                <App />
                <Toaster toastOptions={toastOptions} />
                {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
              </AuthProvider>
            </ThemeProvider>
          </BrowserRouter>
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  </React.StrictMode>,
)
