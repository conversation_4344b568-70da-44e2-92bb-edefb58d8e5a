# Frontend Environment Variables

# API Configuration
VITE_API_URL=http://localhost:8000/api
VITE_API_TIMEOUT=10000

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# reCAPTCHA Configuration
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key

# App Configuration
VITE_APP_NAME=SoloyLibre
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION="Photo-based dating platform"

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PWA=true
VITE_ENABLE_NOTIFICATIONS=true

# Development Configuration
VITE_DEBUG=false
VITE_MOCK_API=false

# Social Login (optional)
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_FACEBOOK_APP_ID=your_facebook_app_id

# CDN Configuration (optional)
VITE_CDN_URL=https://cdn.soloylibre.com

# Sentry Configuration (optional)
VITE_SENTRY_DSN=your_sentry_dsn
