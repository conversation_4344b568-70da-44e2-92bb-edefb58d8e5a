<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="SoloyLibre - Photo-based dating platform connecting people through shared visual interests" />
    
    <!-- Apple-specific meta tags for iPhone-like experience -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="SoloyLibre" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Inter font for modern iOS-like typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <title>SoloyLibre - Find Love Through Photos</title>
    
    <style>
      /* Prevent zoom on iOS */
      input[type="text"], input[type="email"], input[type="password"], textarea {
        font-size: 16px !important;
      }
      
      /* Dark theme base styles */
      body {
        background-color: #000000;
        color: #ffffff;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        overflow-x: hidden;
      }
      
      /* Loading screen */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        font-weight: 700;
        color: white;
        animation: pulse 2s infinite;
        box-shadow: 0 20px 40px rgba(255, 107, 107, 0.3);
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      
      /* Hide scrollbar but keep functionality */
      ::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }
      
      /* Smooth scrolling */
      html {
        scroll-behavior: smooth;
      }
      
      /* iOS-like selection */
      ::selection {
        background-color: rgba(255, 107, 107, 0.3);
      }
    </style>
  </head>
  <body>
    <div id="loading-screen">
      <div class="loading-logo">💕</div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // Hide loading screen when app loads
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
      
      // Prevent zoom on double tap (iOS)
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
      
      // Disable context menu on long press
      document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
      });
    </script>
    
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
