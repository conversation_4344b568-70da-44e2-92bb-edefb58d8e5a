{"name": "soloylibre-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "format": "prettier --write \"src/**/*.{js,jsx,css,md}\""}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "axios": "^1.6.2", "framer-motion": "^10.16.16", "react-spring": "^9.7.3", "react-use-gesture": "^9.1.3", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-intersection-observer": "^9.5.3", "react-swipeable": "^7.0.1", "react-dropzone": "^14.2.3", "react-image-crop": "^10.1.8", "socket.io-client": "^4.7.4", "date-fns": "^2.30.0", "react-helmet-async": "^1.3.0", "react-error-boundary": "^4.0.11", "js-cookie": "^3.0.5", "react-google-recaptcha": "^3.1.0", "@supabase/supabase-js": "^2.38.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "vitest": "^1.0.4", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1"}}