# Environment Configuration for Soloy<PERSON><PERSON>re
# Copy this file to .env and fill in your actual values

# Application Environment
NODE_ENV=development
PORT=8000

# Database Configuration
DATABASE_URL="postgresql://soloylibre_user:secure_password@localhost:5432/soloylibre"
POSTGRES_USER=soloylibre_user
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=soloylibre

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_REFRESH_SECRET=your_refresh_token_secret_also_long_and_random
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# reCAPTCHA Configuration
RECAPTCHA_SITE_KEY=your_recaptcha_site_key
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp
UPLOAD_PATH=./uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=SoloyLibre

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX=5
LOGIN_RATE_LIMIT_WINDOW=900000  # 15 minutes

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Session Configuration
SESSION_SECRET=your_session_secret_key
SESSION_MAX_AGE=86400000  # 24 hours

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Frontend Configuration (for Vite)
VITE_API_URL=http://localhost:8000/api
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key
VITE_APP_NAME=SoloyLibre
VITE_APP_VERSION=1.0.0

# Development Configuration
DEBUG=soloylibre:*
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=true

# Production Configuration (uncomment for production)
# NODE_ENV=production
# HTTPS_ENABLED=true
# SSL_CERT_PATH=/etc/ssl/certs/soloylibre.crt
# SSL_KEY_PATH=/etc/ssl/private/soloylibre.key
# TRUST_PROXY=true

# Analytics Configuration (optional)
# GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
# MIXPANEL_TOKEN=your_mixpanel_token

# Social Login Configuration (optional)
# GOOGLE_CLIENT_ID=your_google_client_id
# GOOGLE_CLIENT_SECRET=your_google_client_secret
# FACEBOOK_APP_ID=your_facebook_app_id
# FACEBOOK_APP_SECRET=your_facebook_app_secret

# Push Notifications Configuration (optional)
# VAPID_PUBLIC_KEY=your_vapid_public_key
# VAPID_PRIVATE_KEY=your_vapid_private_key
# VAPID_SUBJECT=mailto:<EMAIL>

# CDN Configuration (optional)
# CDN_URL=https://cdn.soloylibre.com
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=soloylibre-uploads

# Error Tracking Configuration (optional)
# SENTRY_DSN=your_sentry_dsn
# SENTRY_ENVIRONMENT=development

# Feature Flags (optional)
# ENABLE_PHOTO_FILTERS=true
# ENABLE_VIDEO_UPLOADS=false
# ENABLE_LIVE_CHAT=true
# ENABLE_PREMIUM_FEATURES=false
