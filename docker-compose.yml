version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: soloylibre-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-soloylibre}
      POSTGRES_USER: ${POSTGRES_USER:-soloylibre_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - soloylibre-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-soloylibre_user}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: soloylibre-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - soloylibre-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: soloylibre-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      DATABASE_URL: postgresql://${POSTGRES_USER:-soloylibre_user}:${POSTGRES_PASSWORD:-secure_password}@database:5432/${POSTGRES_DB:-soloylibre}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      RECAPTCHA_SECRET_KEY: ${RECAPTCHA_SECRET_KEY}
      PORT: 8000
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - uploads_data:/app/uploads
    networks:
      - soloylibre-network
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        NODE_ENV: ${NODE_ENV:-development}
    container_name: soloylibre-frontend
    restart: unless-stopped
    environment:
      VITE_API_URL: ${VITE_API_URL:-http://localhost:8000/api}
      VITE_SUPABASE_URL: ${SUPABASE_URL}
      VITE_SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      VITE_RECAPTCHA_SITE_KEY: ${RECAPTCHA_SITE_KEY}
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - soloylibre-network
    depends_on:
      backend:
        condition: service_healthy

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: soloylibre-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./docker/ssl:/etc/nginx/ssl
      - uploads_data:/var/www/uploads
    networks:
      - soloylibre-network
    depends_on:
      - frontend
      - backend

  # Backup Service
  backup:
    build:
      context: ./docker/backup
      dockerfile: Dockerfile
    container_name: soloylibre-backup
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-soloylibre}
      POSTGRES_USER: ${POSTGRES_USER:-soloylibre_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password}
      BACKUP_SCHEDULE: "0 2 * * *"  # Daily at 2 AM
      BACKUP_RETENTION_DAYS: 30
    volumes:
      - postgres_data:/var/lib/postgresql/data:ro
      - uploads_data:/app/uploads:ro
      - backup_data:/backups
    networks:
      - soloylibre-network
    depends_on:
      database:
        condition: service_healthy

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  backup_data:
    driver: local

networks:
  soloylibre-network:
    driver: bridge
