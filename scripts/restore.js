#!/usr/bin/env node

/**
 * SoloyLibre Restore Script
 * 
 * This script restores SoloyLibre application from backup archives
 * including database, uploaded files, and configuration.
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const tar = require('tar');

const execAsync = promisify(exec);

class RestoreManager {
  constructor(backupPath) {
    this.backupPath = backupPath;
    this.restoreDir = './restore-temp';
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Database configuration
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.POSTGRES_DB || 'soloylibre',
      username: process.env.POSTGRES_USER || 'soloylibre_user',
      password: process.env.POSTGRES_PASSWORD
    };
  }

  async init() {
    console.log('🔄 Starting SoloyLibre restore process...');
    console.log(`📦 Backup file: ${this.backupPath}`);
    console.log(`📅 Restore timestamp: ${this.timestamp}`);
    
    // Verify backup file exists
    try {
      await fs.access(this.backupPath);
    } catch (error) {
      throw new Error(`Backup file not found: ${this.backupPath}`);
    }
    
    // Create restore directory
    await fs.mkdir(this.restoreDir, { recursive: true });
  }

  async extractBackup() {
    console.log('📂 Extracting backup archive...');
    
    try {
      await tar.extract({
        file: this.backupPath,
        cwd: this.restoreDir
      });
      
      console.log('✅ Backup extracted successfully');
    } catch (error) {
      throw new Error(`Failed to extract backup: ${error.message}`);
    }
  }

  async validateBackup() {
    console.log('🔍 Validating backup integrity...');
    
    const metadataPath = path.join(this.restoreDir, 'backup-metadata.json');
    
    try {
      const metadataContent = await fs.readFile(metadataPath, 'utf8');
      const metadata = JSON.parse(metadataContent);
      
      console.log(`📋 Backup info:`);
      console.log(`   - Name: ${metadata.backupName}`);
      console.log(`   - Timestamp: ${metadata.timestamp}`);
      console.log(`   - Version: ${metadata.version}`);
      console.log(`   - Environment: ${metadata.environment}`);
      
      // Verify required files exist
      const requiredFiles = ['database.sql'];
      for (const file of requiredFiles) {
        const filePath = path.join(this.restoreDir, file);
        try {
          await fs.access(filePath);
          console.log(`✅ Found required file: ${file}`);
        } catch (error) {
          throw new Error(`Missing required file: ${file}`);
        }
      }
      
      return metadata;
    } catch (error) {
      throw new Error(`Backup validation failed: ${error.message}`);
    }
  }

  async createBackupBeforeRestore() {
    console.log('💾 Creating backup of current state...');
    
    const BackupManager = require('./backup.js');
    const backup = new BackupManager();
    
    try {
      const backupPath = await backup.run();
      console.log(`✅ Current state backed up to: ${backupPath}`);
      return backupPath;
    } catch (error) {
      console.error('⚠️  Failed to create backup of current state:', error.message);
      throw error;
    }
  }

  async stopServices() {
    console.log('⏹️  Stopping application services...');
    
    try {
      // Stop PM2 processes
      try {
        await execAsync('pm2 stop all');
        console.log('✅ PM2 processes stopped');
      } catch (error) {
        console.log('ℹ️  PM2 not running or no processes to stop');
      }
      
      // Stop Docker containers
      try {
        await execAsync('docker-compose down');
        console.log('✅ Docker containers stopped');
      } catch (error) {
        console.log('ℹ️  Docker containers not running');
      }
      
    } catch (error) {
      console.error('⚠️  Error stopping services:', error.message);
    }
  }

  async restoreDatabase() {
    console.log('🗄️  Restoring database...');
    
    const dumpFile = path.join(this.restoreDir, 'database.sql');
    
    try {
      // Drop existing database and recreate
      const dropCommand = [
        'psql',
        `-h ${this.dbConfig.host}`,
        `-p ${this.dbConfig.port}`,
        `-U ${this.dbConfig.username}`,
        '-d postgres',
        `-c "DROP DATABASE IF EXISTS ${this.dbConfig.database};"`,
        `-c "CREATE DATABASE ${this.dbConfig.database};"`
      ].join(' ');
      
      const env = { ...process.env, PGPASSWORD: this.dbConfig.password };
      await execAsync(dropCommand, { env });
      
      // Restore database from dump
      const restoreCommand = [
        'psql',
        `-h ${this.dbConfig.host}`,
        `-p ${this.dbConfig.port}`,
        `-U ${this.dbConfig.username}`,
        `-d ${this.dbConfig.database}`,
        `-f ${dumpFile}`
      ].join(' ');
      
      await execAsync(restoreCommand, { env });
      
      console.log('✅ Database restored successfully');
    } catch (error) {
      throw new Error(`Database restore failed: ${error.message}`);
    }
  }

  async restoreFiles() {
    console.log('📁 Restoring application files...');
    
    const filesToRestore = [
      { src: 'uploads', dest: './uploads' },
      { src: 'config/.env', dest: './.env' },
      { src: 'config/frontend.env', dest: './frontend/.env' },
      { src: 'config/backend.env', dest: './backend/.env' }
    ];
    
    for (const file of filesToRestore) {
      try {
        const srcPath = path.join(this.restoreDir, file.src);
        const destPath = path.resolve(file.dest);
        
        // Check if source exists
        try {
          await fs.access(srcPath);
        } catch (error) {
          console.log(`⚠️  File not found in backup: ${file.src}`);
          continue;
        }
        
        // Create destination directory
        await fs.mkdir(path.dirname(destPath), { recursive: true });
        
        // Copy file or directory
        const stats = await fs.stat(srcPath);
        if (stats.isDirectory()) {
          await this.copyDirectory(srcPath, destPath);
        } else {
          await fs.copyFile(srcPath, destPath);
        }
        
        console.log(`✅ Restored ${file.src} to ${file.dest}`);
      } catch (error) {
        console.error(`❌ Failed to restore ${file.src}:`, error.message);
      }
    }
  }

  async copyDirectory(src, dest) {
    // Remove existing directory
    try {
      await fs.rm(dest, { recursive: true, force: true });
    } catch (error) {
      // Ignore if directory doesn't exist
    }
    
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  async updateDependencies() {
    console.log('📦 Updating dependencies...');
    
    try {
      await execAsync('npm run install:all');
      console.log('✅ Dependencies updated');
    } catch (error) {
      console.error('⚠️  Failed to update dependencies:', error.message);
    }
  }

  async runMigrations() {
    console.log('🔄 Running database migrations...');
    
    try {
      await execAsync('npm run db:migrate');
      console.log('✅ Database migrations completed');
    } catch (error) {
      console.error('⚠️  Database migrations failed:', error.message);
    }
  }

  async startServices() {
    console.log('▶️  Starting application services...');
    
    try {
      // Start with Docker if docker-compose.yml exists
      try {
        await fs.access('./docker-compose.yml');
        await execAsync('docker-compose up -d');
        console.log('✅ Docker services started');
        return;
      } catch (error) {
        // Docker compose not available, try PM2
      }
      
      // Start with PM2
      try {
        await execAsync('pm2 start ecosystem.config.js');
        console.log('✅ PM2 services started');
      } catch (error) {
        console.log('ℹ️  PM2 configuration not found, manual start required');
      }
      
    } catch (error) {
      console.error('⚠️  Error starting services:', error.message);
    }
  }

  async verifyRestore() {
    console.log('🔍 Verifying restore...');
    
    try {
      // Wait a moment for services to start
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check database connection
      const testQuery = [
        'psql',
        `-h ${this.dbConfig.host}`,
        `-p ${this.dbConfig.port}`,
        `-U ${this.dbConfig.username}`,
        `-d ${this.dbConfig.database}`,
        `-c "SELECT COUNT(*) FROM users;"`
      ].join(' ');
      
      const env = { ...process.env, PGPASSWORD: this.dbConfig.password };
      await execAsync(testQuery, { env });
      
      console.log('✅ Database connection verified');
      
      // Check if application is responding
      try {
        const { stdout } = await execAsync('curl -f http://localhost:8000/health');
        console.log('✅ Application health check passed');
      } catch (error) {
        console.log('⚠️  Application health check failed - manual verification required');
      }
      
    } catch (error) {
      console.error('⚠️  Restore verification failed:', error.message);
    }
  }

  async cleanup() {
    console.log('🧹 Cleaning up temporary files...');
    
    try {
      await fs.rm(this.restoreDir, { recursive: true, force: true });
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('⚠️  Cleanup failed:', error.message);
    }
  }

  async run() {
    try {
      await this.init();
      await this.extractBackup();
      const metadata = await this.validateBackup();
      
      // Confirm restore operation
      console.log('\n⚠️  WARNING: This will replace your current application data!');
      console.log('Make sure you have a backup of your current state.');
      
      if (process.env.RESTORE_CONFIRM !== 'yes') {
        console.log('Set RESTORE_CONFIRM=yes to proceed with restore.');
        process.exit(1);
      }
      
      // Create backup of current state
      await this.createBackupBeforeRestore();
      
      // Perform restore
      await this.stopServices();
      await this.restoreDatabase();
      await this.restoreFiles();
      await this.updateDependencies();
      await this.runMigrations();
      await this.startServices();
      await this.verifyRestore();
      
      // Cleanup
      await this.cleanup();
      
      console.log('\n🎉 Restore completed successfully!');
      console.log('Please verify that your application is working correctly.');
      
    } catch (error) {
      console.error('\n💥 Restore failed:', error.message);
      await this.cleanup();
      process.exit(1);
    }
  }
}

// Run restore if called directly
if (require.main === module) {
  const backupPath = process.argv[2];
  
  if (!backupPath) {
    console.error('Usage: node restore.js <backup-file.tar.gz>');
    process.exit(1);
  }
  
  const restore = new RestoreManager(backupPath);
  restore.run();
}

module.exports = RestoreManager;
