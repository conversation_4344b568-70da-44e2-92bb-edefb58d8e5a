#!/usr/bin/env node

/**
 * SoloyLibre Backup Script
 * 
 * This script creates comprehensive backups of the SoloyLibre application
 * including database, uploaded files, and configuration.
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const crypto = require('crypto');
const archiver = require('archiver');

const execAsync = promisify(exec);

class BackupManager {
  constructor() {
    this.backupDir = process.env.BACKUP_DIR || './backups';
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.backupName = `soloylibre-backup-${this.timestamp}`;
    this.tempDir = path.join(this.backupDir, 'temp', this.backupName);
    
    // Database configuration
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.POSTGRES_DB || 'soloylibre',
      username: process.env.POSTGRES_USER || 'soloylibre_user',
      password: process.env.POSTGRES_PASSWORD
    };
  }

  async init() {
    console.log('🚀 Starting SoloyLibre backup process...');
    console.log(`📅 Timestamp: ${this.timestamp}`);
    console.log(`📁 Backup directory: ${this.backupDir}`);
    
    // Create backup directories
    await fs.mkdir(this.tempDir, { recursive: true });
    await fs.mkdir(this.backupDir, { recursive: true });
  }

  async backupDatabase() {
    console.log('🗄️  Backing up database...');
    
    const dumpFile = path.join(this.tempDir, 'database.sql');
    const pgDumpCommand = [
      'pg_dump',
      `-h ${this.dbConfig.host}`,
      `-p ${this.dbConfig.port}`,
      `-U ${this.dbConfig.username}`,
      `-d ${this.dbConfig.database}`,
      `--file=${dumpFile}`,
      '--verbose',
      '--no-password'
    ].join(' ');

    try {
      // Set password environment variable
      const env = { ...process.env, PGPASSWORD: this.dbConfig.password };
      
      await execAsync(pgDumpCommand, { env });
      
      // Verify backup file exists and has content
      const stats = await fs.stat(dumpFile);
      if (stats.size === 0) {
        throw new Error('Database backup file is empty');
      }
      
      console.log(`✅ Database backup completed (${this.formatBytes(stats.size)})`);
      return dumpFile;
    } catch (error) {
      console.error('❌ Database backup failed:', error.message);
      throw error;
    }
  }

  async backupFiles() {
    console.log('📁 Backing up application files...');
    
    const filesToBackup = [
      { src: './uploads', dest: 'uploads', required: false },
      { src: './.env', dest: 'config/.env', required: false },
      { src: './docker-compose.yml', dest: 'config/docker-compose.yml', required: false },
      { src: './package.json', dest: 'config/package.json', required: true },
      { src: './frontend/.env', dest: 'config/frontend.env', required: false },
      { src: './backend/.env', dest: 'config/backend.env', required: false }
    ];

    let totalSize = 0;
    
    for (const file of filesToBackup) {
      try {
        const srcPath = path.resolve(file.src);
        const destPath = path.join(this.tempDir, file.dest);
        
        // Create destination directory
        await fs.mkdir(path.dirname(destPath), { recursive: true });
        
        // Check if source exists
        try {
          await fs.access(srcPath);
        } catch (error) {
          if (file.required) {
            throw new Error(`Required file not found: ${file.src}`);
          } else {
            console.log(`⚠️  Optional file not found: ${file.src}`);
            continue;
          }
        }

        // Copy file or directory
        const stats = await fs.stat(srcPath);
        if (stats.isDirectory()) {
          await this.copyDirectory(srcPath, destPath);
        } else {
          await fs.copyFile(srcPath, destPath);
        }
        
        const size = await this.getDirectorySize(destPath);
        totalSize += size;
        
        console.log(`✅ Backed up ${file.src} (${this.formatBytes(size)})`);
      } catch (error) {
        console.error(`❌ Failed to backup ${file.src}:`, error.message);
        if (file.required) {
          throw error;
        }
      }
    }
    
    console.log(`✅ File backup completed (${this.formatBytes(totalSize)})`);
    return totalSize;
  }

  async copyDirectory(src, dest) {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  async getDirectorySize(dirPath) {
    let totalSize = 0;
    
    try {
      const stats = await fs.stat(dirPath);
      if (stats.isFile()) {
        return stats.size;
      }
      
      if (stats.isDirectory()) {
        const entries = await fs.readdir(dirPath);
        for (const entry of entries) {
          totalSize += await this.getDirectorySize(path.join(dirPath, entry));
        }
      }
    } catch (error) {
      // Ignore errors for individual files
    }
    
    return totalSize;
  }

  async createMetadata() {
    console.log('📋 Creating backup metadata...');
    
    const metadata = {
      backupName: this.backupName,
      timestamp: this.timestamp,
      version: require('../package.json').version,
      environment: process.env.NODE_ENV || 'development',
      database: {
        host: this.dbConfig.host,
        port: this.dbConfig.port,
        database: this.dbConfig.database,
        username: this.dbConfig.username
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version
      },
      checksums: {}
    };

    // Calculate checksums for important files
    const files = await fs.readdir(this.tempDir, { recursive: true });
    for (const file of files) {
      const filePath = path.join(this.tempDir, file);
      try {
        const stats = await fs.stat(filePath);
        if (stats.isFile()) {
          const content = await fs.readFile(filePath);
          metadata.checksums[file] = crypto.createHash('sha256').update(content).digest('hex');
        }
      } catch (error) {
        // Ignore errors for individual files
      }
    }

    const metadataPath = path.join(this.tempDir, 'backup-metadata.json');
    await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    
    console.log('✅ Metadata created');
    return metadata;
  }

  async createArchive() {
    console.log('📦 Creating backup archive...');
    
    const archivePath = path.join(this.backupDir, `${this.backupName}.tar.gz`);
    
    return new Promise((resolve, reject) => {
      const output = require('fs').createWriteStream(archivePath);
      const archive = archiver('tar', { gzip: true });
      
      output.on('close', () => {
        console.log(`✅ Archive created: ${archivePath} (${this.formatBytes(archive.pointer())})`);
        resolve(archivePath);
      });
      
      archive.on('error', reject);
      archive.pipe(output);
      
      // Add all files from temp directory
      archive.directory(this.tempDir, false);
      archive.finalize();
    });
  }

  async cleanup() {
    console.log('🧹 Cleaning up temporary files...');
    
    try {
      await fs.rm(this.tempDir, { recursive: true, force: true });
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('⚠️  Cleanup failed:', error.message);
    }
  }

  async cleanupOldBackups() {
    console.log('🗑️  Cleaning up old backups...');
    
    const retentionDays = parseInt(process.env.BACKUP_RETENTION_DAYS) || 30;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
    
    try {
      const files = await fs.readdir(this.backupDir);
      let deletedCount = 0;
      
      for (const file of files) {
        if (file.startsWith('soloylibre-backup-') && file.endsWith('.tar.gz')) {
          const filePath = path.join(this.backupDir, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime < cutoffDate) {
            await fs.unlink(filePath);
            deletedCount++;
            console.log(`🗑️  Deleted old backup: ${file}`);
          }
        }
      }
      
      console.log(`✅ Cleaned up ${deletedCount} old backup(s)`);
    } catch (error) {
      console.error('⚠️  Old backup cleanup failed:', error.message);
    }
  }

  async uploadToCloud() {
    // Optional: Upload to cloud storage (AWS S3, Google Cloud, etc.)
    if (process.env.BACKUP_CLOUD_ENABLED === 'true') {
      console.log('☁️  Uploading to cloud storage...');
      // Implementation depends on cloud provider
      // Example for AWS S3:
      // await this.uploadToS3(archivePath);
    }
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async run() {
    try {
      await this.init();
      
      // Perform backup operations
      await this.backupDatabase();
      await this.backupFiles();
      await this.createMetadata();
      const archivePath = await this.createArchive();
      
      // Cleanup
      await this.cleanup();
      await this.cleanupOldBackups();
      await this.uploadToCloud();
      
      console.log('🎉 Backup completed successfully!');
      console.log(`📦 Archive: ${archivePath}`);
      
      return archivePath;
    } catch (error) {
      console.error('💥 Backup failed:', error.message);
      await this.cleanup();
      process.exit(1);
    }
  }
}

// Run backup if called directly
if (require.main === module) {
  const backup = new BackupManager();
  backup.run();
}

module.exports = BackupManager;
