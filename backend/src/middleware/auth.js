// DEVELOPMENT MODE: No authentication required
// Always set super admin user for all requests

const SUPER_ADMIN_USER = {
  id: 'super-admin-dev',
  email: 'jose<PERSON><PERSON>@soloylibre.com',
  username: 'jose<PERSON><PERSON>',
  role: '<PERSON><PERSON><PERSON>',
  isActive: true,
  isVerified: true,
  profileData: {
    name: '<PERSON>',
    bio: 'Super Administrator - Development Mode',
    age: 30,
    location: 'Global'
  }
}

// No authentication - always allow access as super admin
const verifyToken = async (req, res, next) => {
  req.user = SUPER_ADMIN_USER
  next()
}

// All auth functions just set super admin and continue
const requireAuth = (req, res, next) => {
  req.user = SUPER_ADMIN_USER
  next()
}

const requireAdmin = (req, res, next) => {
  req.user = SUPER_ADMIN_USER
  next()
}

const requireModerator = (req, res, next) => {
  req.user = SUPER_ADMIN_USER
  next()
}

const optionalAuth = (req, res, next) => {
  req.user = SUPER_ADMIN_USER
  next()
}

module.exports = {
  verifyToken,
  requireAuth,
  requireAdmin,
  requireModerator,
  optionalAuth,
}
