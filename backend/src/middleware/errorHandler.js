const logger = require('../utils/logger')

const errorHandler = (error, req, res, next) => {
  // Log the error
  logger.logError(error, req)

  // Default error response
  let statusCode = 500
  let errorResponse = {
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
    },
  }

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400
    errorResponse.error = {
      code: 'VALIDATION_ERROR',
      message: 'Validation failed',
      details: error.details || error.message,
    }
  } else if (error.name === 'UnauthorizedError' || error.message === 'Unauthorized') {
    statusCode = 401
    errorResponse.error = {
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    }
  } else if (error.name === 'ForbiddenError' || error.message === 'Forbidden') {
    statusCode = 403
    errorResponse.error = {
      code: 'FORBIDDEN',
      message: 'Access denied',
    }
  } else if (error.name === 'NotFoundError' || error.message === 'Not Found') {
    statusCode = 404
    errorResponse.error = {
      code: 'NOT_FOUND',
      message: 'Resource not found',
    }
  } else if (error.name === 'ConflictError' || error.message === 'Conflict') {
    statusCode = 409
    errorResponse.error = {
      code: 'CONFLICT',
      message: 'Resource conflict',
    }
  } else if (error.code === 'P2002') {
    // Prisma unique constraint violation
    statusCode = 409
    errorResponse.error = {
      code: 'DUPLICATE_ENTRY',
      message: 'A record with this information already exists',
    }
  } else if (error.code === 'P2025') {
    // Prisma record not found
    statusCode = 404
    errorResponse.error = {
      code: 'NOT_FOUND',
      message: 'Record not found',
    }
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    errorResponse.error = {
      code: 'INVALID_TOKEN',
      message: 'Invalid authentication token',
    }
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401
    errorResponse.error = {
      code: 'TOKEN_EXPIRED',
      message: 'Authentication token has expired',
    }
  } else if (error.name === 'MulterError') {
    statusCode = 400
    errorResponse.error = {
      code: 'FILE_UPLOAD_ERROR',
      message: error.message,
    }
  }

  // Include error details in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = error.stack
    errorResponse.error.details = error.details || error.message
  }

  res.status(statusCode).json(errorResponse)
}

module.exports = errorHandler
