const express = require('express')
const router = express.Router()

// DEVELOPMENT MODE: No authentication required
// Always return super admin user

const SUPER_ADMIN_USER = {
  id: 'super-admin-dev',
  email: 'jose<PERSON><PERSON>@soloylibre.com',
  username: 'j<PERSON><PERSON><PERSON>',
  role: 'ADMI<PERSON>',
  isActive: true,
  isVerified: true,
  profileData: {
    name: '<PERSON>',
    bio: 'Super Administrator - Development Mode',
    age: 30,
    location: 'Global'
  },
  lastLogin: new Date(),
}

// Mock tokens for development
const DEV_TOKENS = {
  accessToken: 'dev-super-admin-token',
  refreshToken: 'dev-super-admin-refresh-token'
}

// Register - DEVELOPMENT MODE: Always return super admin
router.post('/register', async (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Auto-register as super admin')

  res.status(201).json({
    success: true,
    data: {
      user: SUPER_ADMIN_USER,
      ...DEV_TOKENS,
    },
  })
})

// Login - DEVELOPMENT MODE: Always return super admin
router.post('/login', async (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Auto-login as super admin')

  res.json({
    success: true,
    data: {
      user: SUPER_ADMIN_USER,
      ...DEV_TOKENS,
    },
  })
})

// Logout - DEVELOPMENT MODE: Always success
router.post('/logout', async (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Mock logout')
  res.json({
    success: true,
    message: 'Logged out successfully (Development Mode)',
  })
})

// Refresh token - DEVELOPMENT MODE: Always return same tokens
router.post('/refresh', async (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Mock token refresh')
  res.json({
    success: true,
    data: DEV_TOKENS,
  })
})

module.exports = router
