const express = require('express')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const { PrismaClient } = require('@prisma/client')
const { body, validationResult } = require('express-validator')
const rateLimiter = require('../middleware/rateLimiter')
const { requireAuth } = require('../middleware/auth')
const logger = require('../utils/logger')

const router = express.Router()
const prisma = new PrismaClient()

// Generate JWT tokens
const generateTokens = (userId) => {
  const accessToken = jwt.sign(
    { userId, type: 'access' },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_ACCESS_EXPIRY || '15m' }
  )

  const refreshToken = jwt.sign(
    { userId, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRY || '7d' }
  )

  return { accessToken, refreshToken }
}

// Register
router.post('/register', 
  rateLimiter.auth,
  [
    body('email').isEmail().normalizeEmail(),
    body('username').isLength({ min: 3, max: 30 }).matches(/^[a-zA-Z0-9_]+$/),
    body('password').isLength({ min: 8 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: errors.array(),
          },
        })
      }

      const { email, username, password } = req.body

      // Check if user already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [{ email }, { username }],
        },
      })

      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: {
            code: 'USER_EXISTS',
            message: 'User with this email or username already exists',
          },
        })
      }

      // Hash password
      const passwordHash = await bcrypt.hash(password, 12)

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          username,
          passwordHash,
          isVerified: true, // Auto-verify for demo
        },
        select: {
          id: true,
          email: true,
          username: true,
          role: true,
          createdAt: true,
        },
      })

      // Generate tokens
      const tokens = generateTokens(user.id)

      logger.info(`New user registered: ${user.username} (${user.email})`)

      res.status(201).json({
        success: true,
        data: {
          user,
          ...tokens,
        },
      })
    } catch (error) {
      logger.logError(error, req)
      res.status(500).json({
        success: false,
        error: {
          code: 'REGISTRATION_ERROR',
          message: 'Failed to create account',
        },
      })
    }
  }
)

// Login
router.post('/login',
  rateLimiter.auth,
  [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: errors.array(),
          },
        })
      }

      const { email, password } = req.body

      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
      })

      if (!user || !user.isActive) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password',
          },
        })
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.passwordHash)
      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password',
          },
        })
      }

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
      })

      // Generate tokens
      const tokens = generateTokens(user.id)

      const userResponse = {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        lastLogin: new Date(),
      }

      logger.info(`User logged in: ${user.username} (${user.email})`)

      res.json({
        success: true,
        data: {
          user: userResponse,
          ...tokens,
        },
      })
    } catch (error) {
      logger.logError(error, req)
      res.status(500).json({
        success: false,
        error: {
          code: 'LOGIN_ERROR',
          message: 'Failed to login',
        },
      })
    }
  }
)

// Logout
router.post('/logout', requireAuth, async (req, res) => {
  try {
    logger.info(`User logged out: ${req.user.username}`)
    
    res.json({
      success: true,
      message: 'Logged out successfully',
    })
  } catch (error) {
    logger.logError(error, req)
    res.status(500).json({
      success: false,
      error: {
        code: 'LOGOUT_ERROR',
        message: 'Failed to logout',
      },
    })
  }
})

// Refresh token
router.post('/refresh',
  [body('refreshToken').notEmpty()],
  async (req, res) => {
    try {
      const { refreshToken } = req.body

      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'NO_REFRESH_TOKEN',
            message: 'Refresh token is required',
          },
        })
      }

      // Verify refresh token
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET)
      
      if (decoded.type !== 'refresh') {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN_TYPE',
            message: 'Invalid token type',
          },
        })
      }

      // Generate new tokens
      const tokens = generateTokens(decoded.userId)

      res.json({
        success: true,
        data: tokens,
      })
    } catch (error) {
      if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_REFRESH_TOKEN',
            message: 'Invalid or expired refresh token',
          },
        })
      }

      logger.logError(error, req)
      res.status(500).json({
        success: false,
        error: {
          code: 'REFRESH_ERROR',
          message: 'Failed to refresh token',
        },
      })
    }
  }
)

module.exports = router
