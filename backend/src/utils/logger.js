const winston = require('winston')

// Simple console logger for development mode
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({ format: 'HH:mm:ss' }),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level}]: ${message}`
    })
  ),
  transports: [
    new winston.transports.Console()
  ],
})

// Create a stream object for Morgan
logger.stream = {
  write: (message) => {
    logger.info(message.trim())
  }
}

// Helper methods
logger.logRequest = (req, res, next) => {
  const start = Date.now()
  
  res.on('finish', () => {
    const duration = Date.now() - start
    const logData = {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
    }
    
    if (res.statusCode >= 400) {
      logger.warn('HTTP Request', logData)
    } else {
      logger.info('HTTP Request', logData)
    }
  })
  
  next()
}

logger.logError = (error, req = null) => {
  const errorData = {
    message: error.message,
    stack: error.stack,
    name: error.name,
  }
  
  if (req) {
    errorData.request = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
    }
  }
  
  logger.error('Application Error', errorData)
}

logger.logSecurity = (event, details, req = null) => {
  const securityData = {
    event,
    details,
    timestamp: new Date().toISOString(),
  }
  
  if (req) {
    securityData.request = {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
    }
  }
  
  logger.warn('Security Event', securityData)
}

logger.logAuth = (action, userId, details = {}) => {
  logger.info('Authentication Event', {
    action,
    userId,
    details,
    timestamp: new Date().toISOString(),
  })
}

logger.logDatabase = (operation, table, details = {}) => {
  logger.debug('Database Operation', {
    operation,
    table,
    details,
    timestamp: new Date().toISOString(),
  })
}

// Simple error logging for development
logger.logError = (error, req = null) => {
  logger.error(`Error: ${error.message}`)
  if (process.env.NODE_ENV === 'development') {
    console.error(error.stack)
  }
}

module.exports = logger
