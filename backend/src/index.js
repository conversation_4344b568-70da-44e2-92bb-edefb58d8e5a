require('dotenv').config()
require('express-async-errors')

const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const morgan = require('morgan')
const compression = require('compression')
const cookieParser = require('cookie-parser')
const path = require('path')

const { PrismaClient } = require('@prisma/client')
const logger = require('./utils/logger')
const errorHandler = require('./middleware/errorHandler')
const rateLimiter = require('./middleware/rateLimiter')

// Import routes
const authRoutes = require('./routes/auth')
const userRoutes = require('./routes/users')
const photoRoutes = require('./routes/photos')
const matchRoutes = require('./routes/matches')
const messageRoutes = require('./routes/messages')
const adminRoutes = require('./routes/admin')

// Initialize Prisma client
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
})

// Initialize Express app
const app = express()
const PORT = process.env.PORT || 8000

// Trust proxy if behind reverse proxy
if (process.env.TRUST_PROXY === 'true') {
  app.set('trust proxy', 1)
}

// Security middleware
if (process.env.HELMET_ENABLED !== 'false') {
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
  }))
}

// CORS configuration - DEVELOPMENT MODE: Allow dashboard on port 8888
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:8888',
      'http://127.0.0.1:8888',
      'http://localhost:3000',
      'http://localhost:3001'
    ]

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true)

    if (allowedOrigins.includes(origin)) {
      console.log('🔓 DEVELOPMENT MODE: Allowing CORS for dashboard origin:', origin)
      callback(null, true)
    } else {
      console.log('🔓 DEVELOPMENT MODE: Allowing unknown origin for development:', origin)
      callback(null, true) // Allow all in development
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}

app.use(cors(corsOptions))

// Compression middleware
if (process.env.COMPRESSION_ENABLED !== 'false') {
  app.use(compression())
}

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }))
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))
app.use(cookieParser())

// Rate limiting
app.use(rateLimiter.general)

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')))

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  })
})

// DEVELOPMENT MODE: Serve auto-auth page that redirects to real dashboard
app.get('/dashboard-auto-auth', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Serving auto-auth page')

  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>SoloyLibre - Auto Authentication</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0;
        }
        .container {
          background: white;
          border-radius: 20px;
          padding: 40px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
          max-width: 500px;
          width: 90%;
        }
        .spinner {
          border: 4px solid #f3f3f3;
          border-top: 4px solid #667eea;
          border-radius: 50%;
          width: 50px;
          height: 50px;
          animation: spin 1s linear infinite;
          margin: 20px auto;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .status { color: #667eea; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🔓 SoloyLibre Development Mode</h1>
        <div class="spinner"></div>
        <div class="status">Setting up auto-authentication...</div>
        <div class="status">Bypassing login requirements...</div>
        <div class="status">Configuring super admin access...</div>
        <p><strong>User:</strong> josetusabe (Super Administrator)</p>
        <p><strong>Status:</strong> <span style="color: green;">Authenticated</span></p>
        <p>Redirecting to dashboard...</p>
      </div>

      <script>
        console.log('🔓 DEVELOPMENT MODE: Auto-authentication started');

        const SUPER_ADMIN_USER = {
          id: 'super-admin-dev',
          email: '<EMAIL>',
          username: 'josetusabe',
          role: 'ADMIN',
          isActive: true,
          isVerified: true,
          authenticated: true,
          profileData: {
            name: 'Jose Tusabe',
            bio: 'Super Administrator - Development Mode',
            age: 30,
            location: 'Global'
          }
        };

        // Set authentication data in localStorage
        localStorage.setItem('user', JSON.stringify(SUPER_ADMIN_USER));
        localStorage.setItem('token', 'dev-super-admin-token');
        localStorage.setItem('accessToken', 'dev-super-admin-token');
        localStorage.setItem('refreshToken', 'dev-super-admin-refresh-token');
        localStorage.setItem('authenticated', 'true');
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('authUser', JSON.stringify(SUPER_ADMIN_USER));
        localStorage.setItem('currentUser', JSON.stringify(SUPER_ADMIN_USER));
        localStorage.setItem('auth_token', 'dev-super-admin-token');
        localStorage.setItem('session', JSON.stringify({
          user: SUPER_ADMIN_USER,
          token: 'dev-super-admin-token',
          authenticated: true
        }));

        // Set authentication data in sessionStorage
        sessionStorage.setItem('user', JSON.stringify(SUPER_ADMIN_USER));
        sessionStorage.setItem('token', 'dev-super-admin-token');
        sessionStorage.setItem('authenticated', 'true');

        console.log('✅ DEVELOPMENT MODE: Authentication data set');

        // Redirect to real dashboard after 2 seconds
        setTimeout(() => {
          console.log('🔄 DEVELOPMENT MODE: Redirecting to dashboard');
          window.location.href = 'http://localhost:8888/soloylibre-dashboard-jts-admin';
        }, 2000);
      </script>
    </body>
    </html>
  `)
})

// DEVELOPMENT MODE: Intercept any dashboard auth requests
app.all('/soloylibre-dashboard-jts-admin/auth/*', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Intercepting dashboard auth request:', req.method, req.path)

  const SUPER_ADMIN_USER = {
    id: 'super-admin-dev',
    email: '<EMAIL>',
    username: 'josetusabe',
    role: 'ADMIN',
    isActive: true,
    isVerified: true,
    authenticated: true,
    profileData: {
      name: 'Jose Tusabe',
      bio: 'Super Administrator - Development Mode',
      age: 30,
      location: 'Global'
    }
  }

  res.json({
    success: true,
    authenticated: true,
    bypass: true,
    user: SUPER_ADMIN_USER,
    token: 'dev-super-admin-token',
    accessToken: 'dev-super-admin-token',
    refreshToken: 'dev-super-admin-refresh-token'
  })
})

// DEVELOPMENT MODE: Auto-auth script for dashboard
app.get('/soloylibre-dashboard-jts-admin/auto-auth.js', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Serving auto-auth script')

  res.setHeader('Content-Type', 'application/javascript')
  res.send(`
    // DEVELOPMENT MODE: Auto-authentication script
    console.log('🔓 DEVELOPMENT MODE: Auto-auth script loaded');

    const SUPER_ADMIN_USER = {
      id: 'super-admin-dev',
      email: '<EMAIL>',
      username: 'josetusabe',
      role: 'ADMIN',
      isActive: true,
      isVerified: true,
      authenticated: true,
      profileData: {
        name: 'Jose Tusabe',
        bio: 'Super Administrator - Development Mode',
        age: 30,
        location: 'Global'
      }
    };

    // Set authentication data in localStorage
    localStorage.setItem('user', JSON.stringify(SUPER_ADMIN_USER));
    localStorage.setItem('token', 'dev-super-admin-token');
    localStorage.setItem('accessToken', 'dev-super-admin-token');
    localStorage.setItem('refreshToken', 'dev-super-admin-refresh-token');
    localStorage.setItem('authenticated', 'true');
    localStorage.setItem('isLoggedIn', 'true');
    localStorage.setItem('authUser', JSON.stringify(SUPER_ADMIN_USER));

    // Set authentication data in sessionStorage
    sessionStorage.setItem('user', JSON.stringify(SUPER_ADMIN_USER));
    sessionStorage.setItem('token', 'dev-super-admin-token');
    sessionStorage.setItem('authenticated', 'true');

    // Override fetch to intercept auth requests
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
      console.log('🔓 DEVELOPMENT MODE: Intercepting fetch request to:', url);

      // If it's an auth request, return mock success
      if (url.includes('/auth/') || url.includes('/login') || url.includes('/verify')) {
        console.log('🔓 DEVELOPMENT MODE: Mocking auth request');
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({
            success: true,
            authenticated: true,
            bypass: true,
            user: SUPER_ADMIN_USER,
            token: 'dev-super-admin-token',
            accessToken: 'dev-super-admin-token',
            refreshToken: 'dev-super-admin-refresh-token'
          })
        });
      }

      // For other requests, add auth headers and continue
      if (!options.headers) options.headers = {};
      options.headers['Authorization'] = 'Bearer dev-super-admin-token';
      options.headers['X-Auth-Token'] = 'dev-super-admin-token';

      return originalFetch(url, options);
    };

    // Override XMLHttpRequest for older code
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
      const xhr = new originalXHR();
      const originalOpen = xhr.open;
      const originalSend = xhr.send;

      xhr.open = function(method, url, ...args) {
        console.log('🔓 DEVELOPMENT MODE: Intercepting XHR request to:', url);

        if (url.includes('/auth/') || url.includes('/login') || url.includes('/verify')) {
          console.log('🔓 DEVELOPMENT MODE: Mocking XHR auth request');
          setTimeout(() => {
            Object.defineProperty(xhr, 'status', { value: 200 });
            Object.defineProperty(xhr, 'responseText', {
              value: JSON.stringify({
                success: true,
                authenticated: true,
                bypass: true,
                user: SUPER_ADMIN_USER,
                token: 'dev-super-admin-token'
              })
            });
            xhr.onreadystatechange && xhr.onreadystatechange();
          }, 100);
          return;
        }

        return originalOpen.apply(this, [method, url, ...args]);
      };

      xhr.send = function(data) {
        if (this.url && (this.url.includes('/auth/') || this.url.includes('/login'))) {
          return; // Don't actually send auth requests
        }

        // Add auth headers for real requests
        this.setRequestHeader('Authorization', 'Bearer dev-super-admin-token');
        this.setRequestHeader('X-Auth-Token', 'dev-super-admin-token');

        return originalSend.apply(this, arguments);
      };

      return xhr;
    };

    // Trigger authentication events
    window.dispatchEvent(new CustomEvent('auth-success', {
      detail: { user: SUPER_ADMIN_USER, token: 'dev-super-admin-token' }
    }));

    // Auto-redirect from login page
    if (window.location.pathname.includes('/login')) {
      console.log('🔄 DEVELOPMENT MODE: Redirecting from login to dashboard');
      setTimeout(() => {
        window.location.href = '/soloylibre-dashboard-jts-admin/dashboard';
      }, 1000);
    }

    console.log('✅ DEVELOPMENT MODE: Auto-authentication complete');
  `)
})

// DEVELOPMENT MODE: Intercept dashboard API requests
app.all('/soloylibre-dashboard-jts-admin/api/*', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Intercepting dashboard API request:', req.method, req.path)

  const SUPER_ADMIN_USER = {
    id: 'super-admin-dev',
    email: '<EMAIL>',
    username: 'josetusabe',
    role: 'ADMIN',
    isActive: true,
    isVerified: true,
    authenticated: true
  }

  // Handle different API endpoints
  if (req.path.includes('/login') || req.path.includes('/auth')) {
    res.json({
      success: true,
      authenticated: true,
      bypass: true,
      user: SUPER_ADMIN_USER,
      token: 'dev-super-admin-token',
      redirect: '/dashboard'
    })
  } else if (req.path.includes('/user') || req.path.includes('/profile')) {
    res.json({
      success: true,
      data: SUPER_ADMIN_USER
    })
  } else {
    res.json({
      success: true,
      authenticated: true,
      bypass: true,
      data: {},
      user: SUPER_ADMIN_USER
    })
  }
})





// API routes
const API_PREFIX = `/api/${process.env.API_VERSION || 'v1'}`

app.use(`${API_PREFIX}/auth`, authRoutes)
app.use(`${API_PREFIX}/users`, userRoutes)
app.use(`${API_PREFIX}/photos`, photoRoutes)
app.use(`${API_PREFIX}/matches`, matchRoutes)
app.use(`${API_PREFIX}/messages`, messageRoutes)
app.use(`${API_PREFIX}/admin`, adminRoutes)

// API documentation (development only)
if (process.env.NODE_ENV === 'development' && process.env.ENABLE_SWAGGER === 'true') {
  app.get(`${API_PREFIX}/docs`, (req, res) => {
    res.json({
      message: 'SoloyLibre API Documentation',
      version: '1.0.0',
      endpoints: {
        auth: `${API_PREFIX}/auth`,
        users: `${API_PREFIX}/users`,
        photos: `${API_PREFIX}/photos`,
        matches: `${API_PREFIX}/matches`,
        messages: `${API_PREFIX}/messages`,
        admin: `${API_PREFIX}/admin`,
      },
    })
  })
}

// DEVELOPMENT MODE: Catch any dashboard requests and handle them
app.all('/soloylibre-dashboard-jts-admin/*', (req, res, next) => {
  console.log('🔓 DEVELOPMENT MODE: Dashboard request intercepted:', req.method, req.path)

  // If it's a login page request, redirect to dashboard
  if (req.path.includes('/login')) {
    console.log('🔄 DEVELOPMENT MODE: Redirecting login to dashboard')
    return res.redirect('/soloylibre-dashboard-jts-admin/dashboard')
  }

  // If it's an API request, handle it
  if (req.path.includes('/api/')) {
    const SUPER_ADMIN_USER = {
      id: 'super-admin-dev',
      email: '<EMAIL>',
      username: 'josetusabe',
      role: 'ADMIN',
      isActive: true,
      isVerified: true,
      authenticated: true
    }

    return res.json({
      success: true,
      authenticated: true,
      bypass: true,
      user: SUPER_ADMIN_USER,
      token: 'dev-super-admin-token',
      data: {}
    })
  }

  // For other dashboard requests, continue to next middleware
  next()
})

// 404 handler for API routes
app.use(`${API_PREFIX}/*`, (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'API endpoint not found',
    },
  })
})



// Global error handler
app.use(errorHandler)

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully')
  await prisma.$disconnect()
  process.exit(0)
})

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully')
  await prisma.$disconnect()
  process.exit(0)
})

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Uncaught exception handler
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error)
  process.exit(1)
})

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  logger.info(`🚀 SoloyLibre API server running on port ${PORT}`)
  logger.info(`📚 API documentation available at http://localhost:${PORT}${API_PREFIX}/docs`)
  logger.info(`🏥 Health check available at http://localhost:${PORT}/health`)
  logger.info(`🌍 Environment: ${process.env.NODE_ENV}`)

  console.log('\n🔓 DEVELOPMENT MODE ACTIVE:')
  console.log('✅ Authentication: BYPASSED for all API endpoints')
  console.log('✅ User: josetusabe (Super Admin) - Auto-authenticated')
  console.log('✅ CORS: Enabled for dashboard on port 8888')
  console.log('✅ All API endpoints: OPEN and accessible')
  console.log('\n🎯 Your Dashboard:')
  console.log('   Dashboard: http://localhost:8888/soloylibre-dashboard-jts-admin')
  console.log('   Login: http://localhost:8888/soloylibre-dashboard-jts-admin/login')
  console.log('\n🔧 Backend API:')
  console.log(`   API Base: http://localhost:${PORT}${API_PREFIX}`)
  console.log(`   Health: http://localhost:${PORT}/health`)
  console.log('')
})

// Make prisma available globally
app.locals.prisma = prisma

module.exports = { app, server, prisma }
