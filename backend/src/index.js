require('dotenv').config()
require('express-async-errors')

const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const morgan = require('morgan')
const compression = require('compression')
const cookieParser = require('cookie-parser')
const path = require('path')

const { PrismaClient } = require('@prisma/client')
const logger = require('./utils/logger')
const errorHandler = require('./middleware/errorHandler')
const rateLimiter = require('./middleware/rateLimiter')

// Import routes
const authRoutes = require('./routes/auth')
const userRoutes = require('./routes/users')
const photoRoutes = require('./routes/photos')
const matchRoutes = require('./routes/matches')
const messageRoutes = require('./routes/messages')
const adminRoutes = require('./routes/admin')

// Initialize Prisma client
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
})

// Initialize Express app
const app = express()
const PORT = process.env.PORT || 8000

// Trust proxy if behind reverse proxy
if (process.env.TRUST_PROXY === 'true') {
  app.set('trust proxy', 1)
}

// Security middleware
if (process.env.HELMET_ENABLED !== 'false') {
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
  }))
}

// CORS configuration - DEVELOPMENT MODE: Allow all origins
const corsOptions = {
  origin: function (origin, callback) {
    // DEVELOPMENT MODE: Allow all origins
    console.log('🔓 DEVELOPMENT MODE: Allowing CORS for origin:', origin)
    callback(null, true)
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}

app.use(cors(corsOptions))

// Compression middleware
if (process.env.COMPRESSION_ENABLED !== 'false') {
  app.use(compression())
}

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }))
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))
app.use(cookieParser())

// Rate limiting
app.use(rateLimiter.general)

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')))

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  })
})

// DEVELOPMENT MODE: Dashboard login endpoint (no auth required)
app.post('/soloylibre-dashboard-jts-admin/api/login', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Dashboard login request - BYPASSING ALL AUTH')

  const SUPER_ADMIN_USER = {
    id: 'super-admin-dev',
    email: '<EMAIL>',
    username: 'josetusabe',
    role: 'ADMIN',
    isActive: true,
    isVerified: true,
    authenticated: true,
    bypass: true,
    profileData: {
      name: 'Jose Tusabe',
      bio: 'Super Administrator - Development Mode',
      age: 30,
      location: 'Global'
    },
    lastLogin: new Date(),
  }

  res.json({
    success: true,
    authenticated: true,
    bypass: true,
    redirect: '/dashboard',
    data: {
      user: SUPER_ADMIN_USER,
      accessToken: 'dev-super-admin-token',
      refreshToken: 'dev-super-admin-refresh-token'
    },
  })
})

// DEVELOPMENT MODE: Bypass security verification
app.post('/soloylibre-dashboard-jts-admin/api/verify', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Security verification BYPASSED')

  res.json({
    success: true,
    verified: true,
    bypass: true,
    redirect: '/dashboard',
    message: 'Development mode - verification bypassed'
  })
})

// DEVELOPMENT MODE: Auto-login endpoint
app.get('/soloylibre-dashboard-jts-admin/api/auto-login', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Auto-login triggered')

  const SUPER_ADMIN_USER = {
    id: 'super-admin-dev',
    email: '<EMAIL>',
    username: 'josetusabe',
    role: 'ADMIN',
    isActive: true,
    isVerified: true,
    authenticated: true,
    bypass: true
  }

  res.json({
    success: true,
    authenticated: true,
    bypass: true,
    autoLogin: true,
    redirect: '/dashboard',
    data: {
      user: SUPER_ADMIN_USER,
      accessToken: 'dev-super-admin-token',
      refreshToken: 'dev-super-admin-refresh-token'
    }
  })
})

// DEVELOPMENT MODE: Dashboard user info endpoint
app.get('/soloylibre-dashboard-jts-admin/api/user', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Dashboard user info request')

  const SUPER_ADMIN_USER = {
    id: 'super-admin-dev',
    email: '<EMAIL>',
    username: 'josetusabe',
    role: 'ADMIN',
    isActive: true,
    isVerified: true,
    profileData: {
      name: 'Jose Tusabe',
      bio: 'Super Administrator - Development Mode',
      age: 30,
      location: 'Global'
    },
    stats: {
      photosUploaded: 25,
      likesReceived: 150,
      matchesCount: 45,
      totalUsers: 150,
      totalPhotos: 1250
    }
  }

  res.json({
    success: true,
    data: SUPER_ADMIN_USER
  })
})

// DEVELOPMENT MODE: Dashboard stats endpoint
app.get('/soloylibre-dashboard-jts-admin/api/stats', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Dashboard stats request')

  res.json({
    success: true,
    data: {
      totalUsers: 1250,
      activeUsers: 890,
      totalPhotos: 5670,
      totalMatches: 3450,
      totalMessages: 12890,
      newUsersToday: 45,
      newPhotosToday: 123,
      newMatchesToday: 67
    }
  })
})

// DEVELOPMENT MODE: Dashboard users list endpoint
app.get('/soloylibre-dashboard-jts-admin/api/users', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Dashboard users list request')

  const mockUsers = Array.from({ length: 20 }, (_, i) => ({
    id: `user-${i + 1}`,
    username: `user_${i + 1}`,
    email: `user${i + 1}@example.com`,
    role: i === 0 ? 'ADMIN' : 'USER',
    isActive: Math.random() > 0.1,
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    lastLogin: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
  }))

  res.json({
    success: true,
    data: {
      users: mockUsers,
      total: 1250,
      page: 1,
      limit: 20
    }
  })
})

// DEVELOPMENT MODE: Session check endpoint
app.get('/soloylibre-dashboard-jts-admin/api/session', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Session check - always authenticated')

  res.json({
    success: true,
    authenticated: true,
    bypass: true,
    user: {
      id: 'super-admin-dev',
      email: '<EMAIL>',
      username: 'josetusabe',
      role: 'ADMIN'
    }
  })
})

// DEVELOPMENT MODE: Auth check endpoint
app.get('/soloylibre-dashboard-jts-admin/api/auth/check', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Auth check - always valid')

  res.json({
    success: true,
    authenticated: true,
    valid: true,
    bypass: true
  })
})

// DEVELOPMENT MODE: Logout endpoint (does nothing)
app.post('/soloylibre-dashboard-jts-admin/api/logout', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Logout request - ignored')

  res.json({
    success: true,
    message: 'Development mode - logout ignored'
  })
})

// DEVELOPMENT MODE: Catch all dashboard API requests
app.all('/soloylibre-dashboard-jts-admin/api/*', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Dashboard API request:', req.method, req.path)

  // Always return success for any dashboard API call
  res.json({
    success: true,
    authenticated: true,
    bypass: true,
    message: 'Development mode - endpoint available',
    method: req.method,
    path: req.path,
    data: {
      user: {
        id: 'super-admin-dev',
        email: '<EMAIL>',
        username: 'josetusabe',
        role: 'ADMIN'
      }
    }
  })
})

// DEVELOPMENT MODE: Serve dashboard directly (bypass login page)
app.get('/soloylibre-dashboard-jts-admin/login', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Login page request - redirecting to dashboard')

  // Return a simple redirect page
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>SoloyLibre - Development Mode</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background: #000;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
          margin: 0;
        }
        .container { text-align: center; }
        .spinner {
          border: 4px solid #333;
          border-top: 4px solid #007bff;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          animation: spin 1s linear infinite;
          margin: 20px auto;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🔓 Development Mode</h1>
        <p>Bypassing authentication...</p>
        <p>Auto-logging in as Super Admin: <strong>josetusabe</strong></p>
        <div class="spinner"></div>
        <p>Redirecting to dashboard...</p>
      </div>
      <script>
        // Auto-redirect to dashboard after 2 seconds
        setTimeout(() => {
          window.location.href = '/soloylibre-dashboard-jts-admin/dashboard';
        }, 2000);
      </script>
    </body>
    </html>
  `)
})

// DEVELOPMENT MODE: Dashboard main page
app.get('/soloylibre-dashboard-jts-admin/dashboard', (req, res) => {
  console.log('🔓 DEVELOPMENT MODE: Dashboard access - authenticated as super admin')

  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>SoloyLibre Admin Dashboard - Development Mode</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background: #f5f5f5;
          margin: 0;
          padding: 20px;
        }
        .header {
          background: #007bff;
          color: white;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        .stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 20px;
        }
        .stat-card {
          background: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
          font-size: 2em;
          font-weight: bold;
          color: #007bff;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🎉 SoloyLibre Admin Dashboard</h1>
        <p>Welcome, <strong>Jose Tusabe</strong> (Super Administrator)</p>
        <p>🔓 Development Mode - Authentication Bypassed</p>
      </div>

      <div class="stats">
        <div class="stat-card">
          <div class="stat-number">1,250</div>
          <div>Total Users</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">890</div>
          <div>Active Users</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">5,670</div>
          <div>Total Photos</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">3,450</div>
          <div>Total Matches</div>
        </div>
      </div>

      <div style="background: white; padding: 20px; border-radius: 8px;">
        <h2>🚀 Dashboard Ready</h2>
        <p>✅ Authentication: <strong>Bypassed</strong></p>
        <p>✅ User Role: <strong>Super Administrator</strong></p>
        <p>✅ Access Level: <strong>Full Access</strong></p>
        <p>✅ Backend API: <strong>Connected (Port 8000)</strong></p>

        <h3>Available API Endpoints:</h3>
        <ul>
          <li>GET /soloylibre-dashboard-jts-admin/api/user</li>
          <li>GET /soloylibre-dashboard-jts-admin/api/stats</li>
          <li>GET /soloylibre-dashboard-jts-admin/api/users</li>
          <li>POST /soloylibre-dashboard-jts-admin/api/login</li>
          <li>GET /soloylibre-dashboard-jts-admin/api/session</li>
        </ul>
      </div>
    </body>
    </html>
  `)
})

// API routes
const API_PREFIX = `/api/${process.env.API_VERSION || 'v1'}`

app.use(`${API_PREFIX}/auth`, authRoutes)
app.use(`${API_PREFIX}/users`, userRoutes)
app.use(`${API_PREFIX}/photos`, photoRoutes)
app.use(`${API_PREFIX}/matches`, matchRoutes)
app.use(`${API_PREFIX}/messages`, messageRoutes)
app.use(`${API_PREFIX}/admin`, adminRoutes)

// API documentation (development only)
if (process.env.NODE_ENV === 'development' && process.env.ENABLE_SWAGGER === 'true') {
  app.get(`${API_PREFIX}/docs`, (req, res) => {
    res.json({
      message: 'SoloyLibre API Documentation',
      version: '1.0.0',
      endpoints: {
        auth: `${API_PREFIX}/auth`,
        users: `${API_PREFIX}/users`,
        photos: `${API_PREFIX}/photos`,
        matches: `${API_PREFIX}/matches`,
        messages: `${API_PREFIX}/messages`,
        admin: `${API_PREFIX}/admin`,
      },
    })
  })
}

// 404 handler for API routes
app.use(`${API_PREFIX}/*`, (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'API endpoint not found',
    },
  })
})

// DEVELOPMENT MODE: Catch all dashboard requests
app.get('*', (req, res) => {
  if (req.path.includes('soloylibre-dashboard')) {
    console.log('🔓 DEVELOPMENT MODE: Dashboard request to unknown path:', req.path)
    res.redirect('/soloylibre-dashboard-jts-admin/dashboard')
  } else {
    res.json({
      message: 'SoloyLibre API - Development Mode',
      availableEndpoints: {
        dashboard: `http://localhost:${PORT}/soloylibre-dashboard-jts-admin/dashboard`,
        login: `http://localhost:${PORT}/soloylibre-dashboard-jts-admin/login`,
        api: `http://localhost:${PORT}${API_PREFIX}`,
        health: `http://localhost:${PORT}/health`
      },
      note: 'Authentication is bypassed in development mode'
    })
  }
})

// Global error handler
app.use(errorHandler)

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully')
  await prisma.$disconnect()
  process.exit(0)
})

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully')
  await prisma.$disconnect()
  process.exit(0)
})

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Uncaught exception handler
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error)
  process.exit(1)
})

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  logger.info(`🚀 SoloyLibre API server running on port ${PORT}`)
  logger.info(`📚 API documentation available at http://localhost:${PORT}${API_PREFIX}/docs`)
  logger.info(`🏥 Health check available at http://localhost:${PORT}/health`)
  logger.info(`🎯 Dashboard available at http://localhost:${PORT}/soloylibre-dashboard-jts-admin/dashboard`)
  logger.info(`🔓 Login bypass at http://localhost:${PORT}/soloylibre-dashboard-jts-admin/login`)
  logger.info(`🌍 Environment: ${process.env.NODE_ENV}`)

  console.log('\n🔓 DEVELOPMENT MODE ACTIVE:')
  console.log('✅ Authentication: BYPASSED')
  console.log('✅ User: josetusabe (Super Admin)')
  console.log('✅ Security: DISABLED')
  console.log('✅ CAPTCHA: DISABLED')
  console.log('✅ All endpoints: OPEN')
  console.log('\n🎯 Quick Access:')
  console.log(`   Dashboard: http://localhost:${PORT}/soloylibre-dashboard-jts-admin/dashboard`)
  console.log(`   Login Page: http://localhost:${PORT}/soloylibre-dashboard-jts-admin/login`)
  console.log(`   API: http://localhost:${PORT}${API_PREFIX}`)
  console.log('')
})

// Make prisma available globally
app.locals.prisma = prisma

module.exports = { app, server, prisma }
