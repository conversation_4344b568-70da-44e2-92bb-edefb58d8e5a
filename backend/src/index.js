require('dotenv').config()
require('express-async-errors')

const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const morgan = require('morgan')
const compression = require('compression')
const cookieParser = require('cookie-parser')
const path = require('path')

const { PrismaClient } = require('@prisma/client')
const logger = require('./utils/logger')
const errorHandler = require('./middleware/errorHandler')
const rateLimiter = require('./middleware/rateLimiter')

// Import routes
const authRoutes = require('./routes/auth')
const userRoutes = require('./routes/users')
const photoRoutes = require('./routes/photos')
const matchRoutes = require('./routes/matches')
const messageRoutes = require('./routes/messages')
const adminRoutes = require('./routes/admin')

// Initialize Prisma client
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
})

// Initialize Express app
const app = express()
const PORT = process.env.PORT || 8000

// Trust proxy if behind reverse proxy
if (process.env.TRUST_PROXY === 'true') {
  app.set('trust proxy', 1)
}

// Security middleware
if (process.env.HELMET_ENABLED !== 'false') {
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
  }))
}

// CORS configuration - DEVELOPMENT MODE: Allow dashboard on port 8888
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:8888',
      'http://127.0.0.1:8888',
      'http://localhost:3000',
      'http://localhost:3001'
    ]

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true)

    if (allowedOrigins.includes(origin)) {
      console.log('🔓 DEVELOPMENT MODE: Allowing CORS for dashboard origin:', origin)
      callback(null, true)
    } else {
      console.log('🔓 DEVELOPMENT MODE: Allowing unknown origin for development:', origin)
      callback(null, true) // Allow all in development
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}

app.use(cors(corsOptions))

// Compression middleware
if (process.env.COMPRESSION_ENABLED !== 'false') {
  app.use(compression())
}

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }))
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))
app.use(cookieParser())

// Rate limiting
app.use(rateLimiter.general)

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')))

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  })
})





// API routes
const API_PREFIX = `/api/${process.env.API_VERSION || 'v1'}`

app.use(`${API_PREFIX}/auth`, authRoutes)
app.use(`${API_PREFIX}/users`, userRoutes)
app.use(`${API_PREFIX}/photos`, photoRoutes)
app.use(`${API_PREFIX}/matches`, matchRoutes)
app.use(`${API_PREFIX}/messages`, messageRoutes)
app.use(`${API_PREFIX}/admin`, adminRoutes)

// API documentation (development only)
if (process.env.NODE_ENV === 'development' && process.env.ENABLE_SWAGGER === 'true') {
  app.get(`${API_PREFIX}/docs`, (req, res) => {
    res.json({
      message: 'SoloyLibre API Documentation',
      version: '1.0.0',
      endpoints: {
        auth: `${API_PREFIX}/auth`,
        users: `${API_PREFIX}/users`,
        photos: `${API_PREFIX}/photos`,
        matches: `${API_PREFIX}/matches`,
        messages: `${API_PREFIX}/messages`,
        admin: `${API_PREFIX}/admin`,
      },
    })
  })
}

// 404 handler for API routes
app.use(`${API_PREFIX}/*`, (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'API endpoint not found',
    },
  })
})



// Global error handler
app.use(errorHandler)

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully')
  await prisma.$disconnect()
  process.exit(0)
})

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully')
  await prisma.$disconnect()
  process.exit(0)
})

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Uncaught exception handler
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error)
  process.exit(1)
})

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  logger.info(`🚀 SoloyLibre API server running on port ${PORT}`)
  logger.info(`📚 API documentation available at http://localhost:${PORT}${API_PREFIX}/docs`)
  logger.info(`🏥 Health check available at http://localhost:${PORT}/health`)
  logger.info(`🌍 Environment: ${process.env.NODE_ENV}`)

  console.log('\n🔓 DEVELOPMENT MODE ACTIVE:')
  console.log('✅ Authentication: BYPASSED for all API endpoints')
  console.log('✅ User: josetusabe (Super Admin) - Auto-authenticated')
  console.log('✅ CORS: Enabled for dashboard on port 8888')
  console.log('✅ All API endpoints: OPEN and accessible')
  console.log('\n🎯 Your Dashboard:')
  console.log('   Dashboard: http://localhost:8888/soloylibre-dashboard-jts-admin')
  console.log('   Login: http://localhost:8888/soloylibre-dashboard-jts-admin/login')
  console.log('\n🔧 Backend API:')
  console.log(`   API Base: http://localhost:${PORT}${API_PREFIX}`)
  console.log(`   Health: http://localhost:${PORT}/health`)
  console.log('')
})

// Make prisma available globally
app.locals.prisma = prisma

module.exports = { app, server, prisma }
