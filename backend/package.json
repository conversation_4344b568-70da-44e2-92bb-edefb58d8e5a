{"name": "soloylibre-backend", "version": "1.0.0", "description": "Backend API for SoloyLibre photo-based dating platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "format": "prettier --write \"src/**/*.js\"", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "node prisma/seed.js", "db:reset": "prisma migrate reset", "db:studio": "prisma studio"}, "keywords": ["dating", "api", "nodejs", "express", "postgresql", "prisma"], "author": "SoloyLibre Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "redis": "^4.6.11", "ioredis": "^5.3.2", "@prisma/client": "^5.7.1", "prisma": "^5.7.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "joi": "^17.11.0", "uuid": "^9.0.1", "crypto": "^1.0.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "express-async-errors": "^3.1.1", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-jwt": "^4.0.1", "express-fileupload": "^1.4.3", "node-cron": "^3.0.3", "archiver": "^6.0.1", "tar": "^6.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.1.1", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "prisma": {"seed": "node prisma/seed.js"}}