import { RedisCommandArgument, RedisCommandArguments } from '.';
export declare const FIRST_KEY_INDEX = 1;
interface XSetIdOptions {
    ENTRIESADDED?: number;
    MAXDELETEDID?: RedisCommandArgument;
}
export declare function transformArguments(key: RedisCommandArgument, lastId: RedisCommandArgument, options?: XSetIdOptions): RedisCommandArguments;
export declare function transformReply(): 'OK';
export {};
