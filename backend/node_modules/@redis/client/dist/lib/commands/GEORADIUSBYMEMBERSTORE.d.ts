import { RedisCommandArgument, RedisCommandArguments } from '.';
import { GeoUnits, GeoRadiusStoreOptions } from './generic-transformers';
export { FIRST_KEY_INDEX, IS_READ_ONLY } from './GEORADIUSBYMEMBER';
export declare function transformArguments(key: RedisCommandArgument, member: string, radius: number, unit: GeoUnits, destination: RedisCommandArgument, options?: GeoRadiusStoreOptions): RedisCommandArguments;
export declare function transformReply(): number;
