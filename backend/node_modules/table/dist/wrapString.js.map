{"version": 3, "sources": ["../src/wrapString.js"], "names": ["subject", "size", "subjectSlice", "chunks", "push", "trim"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEA;;;;;;;;;;;oBAWgBA,O,EAASC,I,KAAS;AAChC,MAAIC,YAAJ;AAEAA,EAAAA,YAAY,GAAGF,OAAf;AAEA,QAAMG,MAAM,GAAG,EAAf;;AAEA,KAAG;AACDA,IAAAA,MAAM,CAACC,IAAP,CAAY,wBAAMF,YAAN,EAAoB,CAApB,EAAuBD,IAAvB,CAAZ;AAEAC,IAAAA,YAAY,GAAG,wBAAMA,YAAN,EAAoBD,IAApB,EAA0BI,IAA1B,EAAf;AACD,GAJD,QAIS,0BAAYH,YAAZ,CAJT;;AAMA,SAAOC,MAAP;AACD,C", "sourcesContent": ["import slice from 'slice-ansi';\nimport stringWidth from 'string-width';\n\n/**\n * Creates an array of strings split into groups the length of size.\n * This function works with strings that contain ASCII characters.\n *\n * wrapText is different from would-be \"chunk\" implementation\n * in that whitespace characters that occur on a chunk size limit are trimmed.\n *\n * @param {string} subject\n * @param {number} size\n * @returns {Array}\n */\nexport default (subject, size) => {\n  let subjectSlice;\n\n  subjectSlice = subject;\n\n  const chunks = [];\n\n  do {\n    chunks.push(slice(subjectSlice, 0, size));\n\n    subjectSlice = slice(subjectSlice, size).trim();\n  } while (stringWidth(subjectSlice));\n\n  return chunks;\n};\n"], "file": "wrapString.js"}