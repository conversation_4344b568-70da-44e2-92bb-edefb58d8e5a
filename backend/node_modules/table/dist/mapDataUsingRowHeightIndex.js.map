{"version": 3, "sources": ["../src/mapDataUsingRowHeightIndex.js"], "names": ["unmappedRows", "rowHeightIndex", "config", "tableWidth", "length", "mappedRows", "map", "cells", "index0", "rowHeight", "Array", "fill", "for<PERSON>ach", "value", "index1", "cellLines", "columns", "width", "wrapWord", "cellLine", "index2"], "mappings": ";;;;;;;;;;;AACA;;;;AAEA;;;;;;oCAMgBA,Y,EAAcC,c,EAAgBC,M,KAAW;AACvD,QAAMC,UAAU,GAAGH,YAAY,CAAC,CAAD,CAAZ,CAAgBI,MAAnC;AAEA,QAAMC,UAAU,GAAGL,YAAY,CAACM,GAAb,CAAiB,CAACC,KAAD,EAAQC,MAAR,KAAmB;AACrD,UAAMC,SAAS,GAAG,qBAAQR,cAAc,CAACO,MAAD,CAAtB,EAAgC,MAAM;AACtD,aAAO,IAAIE,KAAJ,CAAUP,UAAV,EAAsBQ,IAAtB,CAA2B,EAA3B,CAAP;AACD,KAFiB,CAAlB,CADqD,CAKrD;AACA;AACA;;AAEAJ,IAAAA,KAAK,CAACK,OAAN,CAAc,CAACC,KAAD,EAAQC,MAAR,KAAmB;AAC/B,YAAMC,SAAS,GAAG,uBAASF,KAAT,EAAgBX,MAAM,CAACc,OAAP,CAAeF,MAAf,EAAuBG,KAAvC,EAA8Cf,MAAM,CAACc,OAAP,CAAeF,MAAf,EAAuBI,QAArE,CAAlB;AAEAH,MAAAA,SAAS,CAACH,OAAV,CAAkB,CAACO,QAAD,EAAWC,MAAX,KAAsB;AACtCX,QAAAA,SAAS,CAACW,MAAD,CAAT,CAAkBN,MAAlB,IAA4BK,QAA5B;AACD,OAFD;AAGD,KAND;AAQA,WAAOV,SAAP;AACD,GAlBkB,CAAnB;AAoBA,SAAO,uBAAUJ,UAAV,CAAP;AACD,C", "sourcesContent": ["import _ from 'lodash';\nimport wrapCell from './wrapCell';\n\n/**\n * @param {Array} unmappedRows\n * @param {number[]} rowHeightIndex\n * @param {Object} config\n * @returns {Array}\n */\nexport default (unmappedRows, rowHeightIndex, config) => {\n  const tableWidth = unmappedRows[0].length;\n\n  const mappedRows = unmappedRows.map((cells, index0) => {\n    const rowHeight = _.times(rowHeightIndex[index0], () => {\n      return new Array(tableWidth).fill('');\n    });\n\n    // rowHeight\n    //     [{row index within rowSaw; index2}]\n    //     [{cell index within a virtual row; index1}]\n\n    cells.forEach((value, index1) => {\n      const cellLines = wrapCell(value, config.columns[index1].width, config.columns[index1].wrapWord);\n\n      cellLines.forEach((cellLine, index2) => {\n        rowHeight[index2][index1] = cellLine;\n      });\n    });\n\n    return rowHeight;\n  });\n\n  return _.flatten(mappedRows);\n};\n"], "file": "mapDataUsingRowHeightIndex.js"}