{"version": 3, "sources": ["../src/stringifyTableData.js"], "names": ["rows", "map", "cells", "String"], "mappings": ";;;;;;;AAAA;;;;;;2BAMgBA,I,IAAS;AACvB,SAAOA,IAAI,CAACC,GAAL,CAAUC,KAAD,IAAW;AACzB,WAAOA,KAAK,CAACD,GAAN,CAAUE,MAAV,CAAP;AACD,GAFM,CAAP;AAGD,C", "sourcesContent": ["/**\n * Casts all cell values to a string.\n *\n * @param {table~row[]} rows\n * @returns {table~row[]}\n */\nexport default (rows) => {\n  return rows.map((cells) => {\n    return cells.map(String);\n  });\n};\n"], "file": "stringifyTableData.js"}