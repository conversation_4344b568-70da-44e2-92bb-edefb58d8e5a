# Backend Environment Variables

# Application Configuration
NODE_ENV=development
PORT=8000
API_VERSION=v1

# Database Configuration
DATABASE_URL="postgresql://soloylibre_user:secure_password@localhost:5432/soloylibre"

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_REFRESH_SECRET=your_refresh_token_secret_also_long_and_random
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Encryption Configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here
BCRYPT_ROUNDS=12

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX=5
LOGIN_RATE_LIMIT_WINDOW=900000

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp
UPLOAD_PATH=./uploads
UPLOAD_URL=http://localhost:8000/uploads

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=SoloyLibre

# reCAPTCHA Configuration
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key

# Supabase Configuration (for file storage)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_BUCKET_NAME=photos

# Session Configuration
SESSION_SECRET=your_session_secret_key
SESSION_MAX_AGE=86400000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Security Configuration
TRUST_PROXY=false
HELMET_ENABLED=true
COMPRESSION_ENABLED=true

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=false
ENABLE_TWO_FACTOR=false
ENABLE_SOCIAL_LOGIN=false

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Development Configuration
DEBUG=soloylibre:*
ENABLE_SWAGGER=true
MOCK_EXTERNAL_SERVICES=false

# Production Configuration (uncomment for production)
# NODE_ENV=production
# HTTPS_ENABLED=true
# SSL_CERT_PATH=/etc/ssl/certs/soloylibre.crt
# SSL_KEY_PATH=/etc/ssl/private/soloylibre.key

# External Services
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Analytics
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Error Tracking
SENTRY_DSN=your_sentry_dsn

# CDN Configuration
CDN_URL=https://cdn.soloylibre.com
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=soloylibre-uploads
