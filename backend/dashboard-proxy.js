const express = require('express')
const { createProxyMiddleware } = require('http-proxy-middleware')
const app = express()

// DEVELOPMENT MODE: Dashboard Proxy Server
// This server runs on port 8889 and proxies to the real dashboard on 8888
// while intercepting and bypassing all authentication

const SUPER_ADMIN_USER = {
  id: 'super-admin-dev',
  email: 'jose<PERSON><PERSON>@soloylibre.com',
  username: 'jose<PERSON><PERSON>',
  role: 'ADMIN',
  isActive: true,
  isVerified: true,
  authenticated: true,
  profileData: {
    name: '<PERSON>',
    bio: 'Super Administrator - Development Mode',
    age: 30,
    location: 'Global'
  }
}

// Enable CORS for all requests
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Methods', '*')
  res.header('Access-Control-Allow-Headers', '*')
  next()
})

// Parse JSON bodies
app.use(express.json())

// DEVELOPMENT MODE: Intercept login page and redirect to dashboard
app.get('/soloylibre-dashboard-jts-admin/login', (req, res) => {
  console.log('🔓 PROXY: Intercepting login page - redirecting to dashboard')
  res.redirect('/soloylibre-dashboard-jts-admin/dashboard')
})

// DEVELOPMENT MODE: Intercept any auth API requests
app.all('/soloylibre-dashboard-jts-admin/api/auth/*', (req, res) => {
  console.log('🔓 PROXY: Intercepting auth API request:', req.method, req.path)
  
  res.json({
    success: true,
    authenticated: true,
    bypass: true,
    user: SUPER_ADMIN_USER,
    token: 'dev-super-admin-token',
    accessToken: 'dev-super-admin-token',
    refreshToken: 'dev-super-admin-refresh-token',
    redirect: '/dashboard'
  })
})

// DEVELOPMENT MODE: Intercept login POST requests
app.post('/soloylibre-dashboard-jts-admin/api/login', (req, res) => {
  console.log('🔓 PROXY: Intercepting login POST - auto-authenticating')
  
  res.json({
    success: true,
    authenticated: true,
    bypass: true,
    user: SUPER_ADMIN_USER,
    token: 'dev-super-admin-token',
    accessToken: 'dev-super-admin-token',
    refreshToken: 'dev-super-admin-refresh-token',
    redirect: '/dashboard'
  })
})

// DEVELOPMENT MODE: Intercept user info requests
app.get('/soloylibre-dashboard-jts-admin/api/user', (req, res) => {
  console.log('🔓 PROXY: Intercepting user info request')
  
  res.json({
    success: true,
    data: SUPER_ADMIN_USER
  })
})

// DEVELOPMENT MODE: Intercept session check requests
app.get('/soloylibre-dashboard-jts-admin/api/session', (req, res) => {
  console.log('🔓 PROXY: Intercepting session check')
  
  res.json({
    success: true,
    authenticated: true,
    valid: true,
    user: SUPER_ADMIN_USER
  })
})

// DEVELOPMENT MODE: Catch all other API requests
app.all('/soloylibre-dashboard-jts-admin/api/*', (req, res) => {
  console.log('🔓 PROXY: Intercepting API request:', req.method, req.path)
  
  res.json({
    success: true,
    authenticated: true,
    bypass: true,
    user: SUPER_ADMIN_USER,
    data: {},
    message: 'Development mode - endpoint available'
  })
})

// DEVELOPMENT MODE: Serve a simple dashboard page
app.get('/soloylibre-dashboard-jts-admin/dashboard', (req, res) => {
  console.log('🔓 PROXY: Serving dashboard page')
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>SoloyLibre Admin Dashboard - Development Mode</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .container {
          background: white;
          border-radius: 20px;
          padding: 40px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
          max-width: 600px;
          width: 90%;
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 30px;
          border-radius: 15px;
          margin-bottom: 30px;
        }
        .status {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 20px;
          margin: 30px 0;
        }
        .status-item {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 10px;
          border-left: 4px solid #667eea;
        }
        .status-item h3 {
          color: #667eea;
          margin-bottom: 10px;
        }
        .btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 15px 30px;
          border: none;
          border-radius: 10px;
          font-size: 16px;
          cursor: pointer;
          margin: 10px;
          text-decoration: none;
          display: inline-block;
        }
        .btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 SoloyLibre Admin Dashboard</h1>
          <p>Welcome, <strong>Jose Tusabe</strong> (Super Administrator)</p>
          <p>🔓 Development Mode - Authentication Bypassed</p>
        </div>
        
        <div class="status">
          <div class="status-item">
            <h3>✅ Authentication</h3>
            <p>Bypassed</p>
          </div>
          <div class="status-item">
            <h3>👑 User Role</h3>
            <p>Super Admin</p>
          </div>
          <div class="status-item">
            <h3>🔧 Backend API</h3>
            <p>Connected</p>
          </div>
          <div class="status-item">
            <h3>🎯 Access Level</h3>
            <p>Full Access</p>
          </div>
        </div>
        
        <div>
          <h2>🚀 Dashboard Ready</h2>
          <p>All modules and widgets are available with full admin access.</p>
          <p>No login required - you're automatically authenticated as the super administrator.</p>
          
          <div style="margin-top: 30px;">
            <a href="/api/v1" class="btn">🔧 API Documentation</a>
            <a href="/health" class="btn">🏥 Health Check</a>
          </div>
        </div>
      </div>
      
      <script>
        // Auto-authenticate user
        localStorage.setItem('user', JSON.stringify(${JSON.stringify(SUPER_ADMIN_USER)}));
        localStorage.setItem('token', 'dev-super-admin-token');
        localStorage.setItem('authenticated', 'true');
        
        console.log('🔓 DEVELOPMENT MODE: User auto-authenticated');
        console.log('User:', ${JSON.stringify(SUPER_ADMIN_USER)});
      </script>
    </body>
    </html>
  `)
})

// DEVELOPMENT MODE: Catch all other dashboard requests
app.get('/soloylibre-dashboard-jts-admin*', (req, res) => {
  console.log('🔓 PROXY: Redirecting dashboard request to main dashboard')
  res.redirect('/soloylibre-dashboard-jts-admin/dashboard')
})

// Proxy to real dashboard and inject auto-auth script
app.use('/soloylibre-dashboard-jts-admin', (req, res, next) => {
  console.log('🔓 PROXY: Proxying request to real dashboard:', req.path)

  // If it's the main page, inject our auto-auth script
  if (req.path === '/' || req.path === '' || req.path === '/login') {
    console.log('🔓 PROXY: Injecting auto-auth script into dashboard')

    // Fetch the real dashboard HTML
    const http = require('http')
    const options = {
      hostname: 'localhost',
      port: 8888,
      path: '/soloylibre-dashboard-jts-admin/',
      method: 'GET'
    }

    const proxyReq = http.request(options, (proxyRes) => {
      let data = ''
      proxyRes.on('data', (chunk) => {
        data += chunk
      })

      proxyRes.on('end', () => {
        // Inject auto-auth script before closing body tag
        const autoAuthScript = `
          <script>
            // DEVELOPMENT MODE: Auto-authentication
            console.log('🔓 DEVELOPMENT MODE: Auto-auth injected');

            const SUPER_ADMIN_USER = {
              id: 'super-admin-dev',
              email: '<EMAIL>',
              username: 'josetusabe',
              role: 'ADMIN',
              isActive: true,
              isVerified: true,
              authenticated: true
            };

            // Set auth data
            localStorage.setItem('user', JSON.stringify(SUPER_ADMIN_USER));
            localStorage.setItem('token', 'dev-super-admin-token');
            localStorage.setItem('authenticated', 'true');
            localStorage.setItem('isLoggedIn', 'true');

            // Override fetch for auth requests
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
              if (url.includes('/auth') || url.includes('/login') || url.includes('/verify')) {
                console.log('🔓 DEVELOPMENT MODE: Mocking auth request to:', url);
                return Promise.resolve({
                  ok: true,
                  status: 200,
                  json: () => Promise.resolve({
                    success: true,
                    authenticated: true,
                    user: SUPER_ADMIN_USER,
                    token: 'dev-super-admin-token'
                  })
                });
              }
              return originalFetch(url, options);
            };

            // Auto-redirect from login
            if (window.location.pathname.includes('/login')) {
              setTimeout(() => {
                window.location.href = '/soloylibre-dashboard-jts-admin/dashboard';
              }, 500);
            }

            console.log('✅ DEVELOPMENT MODE: Auto-auth complete');
          </script>
        `

        const modifiedHtml = data.replace('</body>', autoAuthScript + '</body>')

        res.setHeader('Content-Type', 'text/html')
        res.send(modifiedHtml)
      })
    })

    proxyReq.on('error', (err) => {
      console.error('Proxy error:', err)
      res.status(500).send('Proxy error')
    })

    proxyReq.end()
  } else {
    // For other requests, just proxy normally
    next()
  }
})

// Proxy all other requests to the real dashboard
app.use('/soloylibre-dashboard-jts-admin', createProxyMiddleware({
  target: 'http://localhost:8888',
  changeOrigin: true,
  logLevel: 'debug'
}))

// Start proxy server
const PORT = 8889
app.listen(PORT, () => {
  console.log(`🔓 DEVELOPMENT PROXY SERVER RUNNING ON PORT ${PORT}`)
  console.log('✅ Authentication: COMPLETELY BYPASSED')
  console.log('✅ User: josetusabe (Super Admin)')
  console.log('✅ Login page: DISABLED')
  console.log('✅ Auto-auth script: INJECTED')
  console.log('')
  console.log('🎯 Dashboard Access (With Auto-Auth):')
  console.log(`   http://localhost:${PORT}/soloylibre-dashboard-jts-admin`)
  console.log(`   http://localhost:${PORT}/soloylibre-dashboard-jts-admin/login`)
  console.log('')
  console.log('🔧 Original Dashboard (Without Auto-Auth):')
  console.log('   http://localhost:8888/soloylibre-dashboard-jts-admin')
  console.log('')
})
