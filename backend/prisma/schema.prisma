// Prisma schema for SoloyLibre

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  username      String   @unique
  passwordHash  String   @map("password_hash")
  role          Role     @default(USER)
  isActive      Boolean  @default(true) @map("is_active")
  isVerified    Boolean  @default(false) @map("is_verified")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  lastLogin     DateTime? @map("last_login")
  
  // Profile information
  profileData   Json?    @map("profile_data")
  avatar        String?
  bio           String?
  age           Int?
  location      String?
  interests     String[]
  
  // Privacy settings
  isPrivate     <PERSON>olean  @default(false) @map("is_private")
  showAge       Boolean  @default(true) @map("show_age")
  showLocation  Boolean  @default(true) @map("show_location")
  
  // Statistics
  photoCount    Int      @default(0) @map("photo_count")
  likeCount     Int      @default(0) @map("like_count")
  matchCount    Int      @default(0) @map("match_count")
  
  // Relationships
  photos        Photo[]
  interactions  PhotoInteraction[]
  matchesAsUser1 Match[] @relation("User1Matches")
  matchesAsUser2 Match[] @relation("User2Matches")
  sentMessages  Message[] @relation("SentMessages")
  receivedMessages Message[] @relation("ReceivedMessages")
  reports       Report[] @relation("ReportedBy")
  reportedUsers Report[] @relation("ReportedUser")
  blockedUsers  Block[]  @relation("BlockedBy")
  blockedBy     Block[]  @relation("BlockedUser")
  
  @@map("users")
}

model Photo {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  fileUrl         String   @map("file_url")
  thumbnailUrl    String?  @map("thumbnail_url")
  title           String?
  description     String?
  tags            String[]
  isProfilePhoto  Boolean  @default(false) @map("is_profile_photo")
  isPublic        Boolean  @default(true) @map("is_public")
  uploadDate      DateTime @default(now()) @map("upload_date")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  // Statistics
  viewCount       Int      @default(0) @map("view_count")
  likeCount       Int      @default(0) @map("like_count")
  commentCount    Int      @default(0) @map("comment_count")
  
  // Metadata
  fileSize        Int?     @map("file_size")
  mimeType        String?  @map("mime_type")
  width           Int?
  height          Int?
  
  // Relationships
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  interactions    PhotoInteraction[]
  
  @@map("photos")
}

model PhotoInteraction {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  photoId         String   @map("photo_id")
  type            InteractionType
  commentText     String?  @map("comment_text")
  createdAt       DateTime @default(now()) @map("created_at")
  
  // Relationships
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  photo           Photo    @relation(fields: [photoId], references: [id], onDelete: Cascade)
  
  @@unique([userId, photoId, type])
  @@map("photo_interactions")
}

model Match {
  id          String      @id @default(cuid())
  user1Id     String      @map("user1_id")
  user2Id     String      @map("user2_id")
  status      MatchStatus @default(PENDING)
  matchScore  Float       @map("match_score")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  
  // Match details
  commonInterests String[] @map("common_interests")
  mutualLikes     Int      @default(0) @map("mutual_likes")
  
  // Relationships
  user1       User     @relation("User1Matches", fields: [user1Id], references: [id], onDelete: Cascade)
  user2       User     @relation("User2Matches", fields: [user2Id], references: [id], onDelete: Cascade)
  messages    Message[]
  
  @@unique([user1Id, user2Id])
  @@map("matches")
}

model Message {
  id          String      @id @default(cuid())
  matchId     String      @map("match_id")
  senderId    String      @map("sender_id")
  receiverId  String      @map("receiver_id")
  content     String
  type        MessageType @default(TEXT)
  isRead      Boolean     @default(false) @map("is_read")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  
  // Relationships
  match       Match    @relation(fields: [matchId], references: [id], onDelete: Cascade)
  sender      User     @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  receiver    User     @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)
  
  @@map("messages")
}

model Report {
  id          String     @id @default(cuid())
  reportedById String    @map("reported_by_id")
  reportedUserId String  @map("reported_user_id")
  reason      ReportReason
  description String?
  status      ReportStatus @default(PENDING)
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")
  
  // Relationships
  reportedBy  User       @relation("ReportedBy", fields: [reportedById], references: [id], onDelete: Cascade)
  reportedUser User      @relation("ReportedUser", fields: [reportedUserId], references: [id], onDelete: Cascade)
  
  @@map("reports")
}

model Block {
  id          String   @id @default(cuid())
  blockedById String   @map("blocked_by_id")
  blockedUserId String @map("blocked_user_id")
  createdAt   DateTime @default(now()) @map("created_at")
  
  // Relationships
  blockedBy   User     @relation("BlockedBy", fields: [blockedById], references: [id], onDelete: Cascade)
  blockedUser User     @relation("BlockedUser", fields: [blockedUserId], references: [id], onDelete: Cascade)
  
  @@unique([blockedById, blockedUserId])
  @@map("blocks")
}

model AuditLog {
  id          String   @id @default(cuid())
  userId      String?  @map("user_id")
  action      String
  resource    String
  resourceId  String?  @map("resource_id")
  oldValues   Json?    @map("old_values")
  newValues   Json?    @map("new_values")
  ipAddress   String?  @map("ip_address")
  userAgent   String?  @map("user_agent")
  createdAt   DateTime @default(now()) @map("created_at")
  
  @@map("audit_logs")
}

// Enums
enum Role {
  USER
  MODERATOR
  ADMIN
}

enum InteractionType {
  LIKE
  DISLIKE
  COMMENT
  VIEW
}

enum MatchStatus {
  PENDING
  ACCEPTED
  REJECTED
  EXPIRED
}

enum MessageType {
  TEXT
  IMAGE
  EMOJI
}

enum ReportReason {
  INAPPROPRIATE_CONTENT
  HARASSMENT
  SPAM
  FAKE_PROFILE
  UNDERAGE
  OTHER
}

enum ReportStatus {
  PENDING
  REVIEWED
  RESOLVED
  DISMISSED
}
