{
  message: '🚀 SoloyLibre API server running on port 8000',
  level: 'info',
  service: 'soloylibre-api',
  timestamp: '2025-06-14 20:21:20'
}
{
  message: '📚 API documentation available at http://localhost:8000/api/v1/docs',
  level: 'info',
  service: 'soloylibre-api',
  timestamp: '2025-06-14 20:21:20'
}
{
  message: '🏥 Health check available at http://localhost:8000/health',
  level: 'info',
  service: 'soloylibre-api',
  timestamp: '2025-06-14 20:21:20'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'soloylibre-api',
  timestamp: '2025-06-14 20:21:20'
}
{
  message: '127.0.0.1 - - [15/Jun/2025:00:21:59 +0000] "GET /health HTTP/1.1" 200 127 "-" "curl/8.7.1"',
  level: 'info',
  service: 'soloylibre-api',
  timestamp: '2025-06-14 20:21:59'
}
