{"name": "soloylibre-app", "version": "1.0.0", "description": "Photo-based dating platform connecting people through shared visual interests", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "npm run start:backend", "start:frontend": "cd frontend && npm run preview", "start:backend": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "format": "npm run format:frontend && npm run format:backend", "format:frontend": "cd frontend && npm run format", "format:backend": "cd backend && npm run format", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "db:setup": "cd backend && npx prisma generate && npx prisma migrate deploy && npx prisma db seed", "db:migrate": "cd backend && npx prisma migrate dev", "db:reset": "cd backend && npx prisma migrate reset", "db:seed": "cd backend && npx prisma db seed", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "security:audit": "npm audit && cd frontend && npm audit && cd ../backend && npm audit", "security:fix": "npm audit fix && cd frontend && npm audit fix && cd ../backend && npm audit fix"}, "keywords": ["dating", "photos", "social", "matching", "react", "nodejs", "postgresql"], "author": "SoloyLibre Development Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "nodemon": "^3.0.2"}, "dependencies": {"dotenv": "^16.3.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/soloylibre-app.git"}, "bugs": {"url": "https://github.com/your-org/soloylibre-app/issues"}, "homepage": "https://github.com/your-org/soloylibre-app#readme"}