# SoloyLibre API Reference

## 📋 Table of Contents

1. [Authentication](#authentication)
2. [User Management](#user-management)
3. [Photo Management](#photo-management)
4. [Interactions](#interactions)
5. [Matching System](#matching-system)
6. [Messaging](#messaging)
7. [Admin Endpoints](#admin-endpoints)
8. [Error Handling](#error-handling)

## 🔐 Authentication

### Base URL
```
Production: https://api.soloylibre.com
Development: http://localhost:8000/api
```

### Authentication Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Register User
```http
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "SecurePass123!",
  "recaptchaToken": "recaptcha_response"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "username": "username",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "accessToken": "jwt_access_token",
    "refreshToken": "jwt_refresh_token"
  }
}
```

### Login User
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "recaptchaToken": "recaptcha_response"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "username": "username",
      "lastLogin": "2024-01-01T00:00:00Z"
    },
    "accessToken": "jwt_access_token",
    "refreshToken": "jwt_refresh_token"
  }
}
```

### Refresh Token
```http
POST /auth/refresh
```

**Request Body:**
```json
{
  "refreshToken": "jwt_refresh_token"
}
```

### Logout
```http
POST /auth/logout
```

**Headers:** `Authorization: Bearer <access_token>`

## 👤 User Management

### Get Current User Profile
```http
GET /users/me
```

**Headers:** `Authorization: Bearer <access_token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "username": "username",
    "profileData": {
      "name": "John Doe",
      "age": 28,
      "bio": "Photography enthusiast",
      "interests": ["photography", "travel", "nature"],
      "location": "San Francisco, CA"
    },
    "stats": {
      "photosUploaded": 15,
      "likesReceived": 234,
      "matchesCount": 12
    }
  }
}
```

### Update User Profile
```http
PUT /users/me
```

**Request Body:**
```json
{
  "profileData": {
    "name": "John Doe",
    "age": 28,
    "bio": "Updated bio",
    "interests": ["photography", "travel", "nature", "art"],
    "location": "San Francisco, CA"
  }
}
```

### Get User by ID
```http
GET /users/:userId
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "user_id",
    "username": "username",
    "profileData": {
      "name": "John Doe",
      "bio": "Photography enthusiast",
      "interests": ["photography", "travel"]
    },
    "photos": [
      {
        "id": "photo_id",
        "fileUrl": "/uploads/photo.jpg",
        "title": "Mountain Sunset",
        "likeCount": 45
      }
    ]
  }
}
```

## 📸 Photo Management

### Upload Photo
```http
POST /photos
```

**Content-Type:** `multipart/form-data`

**Form Data:**
- `file`: Image file (JPG, PNG, GIF, max 10MB)
- `title`: Photo title (optional)
- `description`: Photo description (optional)
- `tags`: Comma-separated tags
- `isProfilePhoto`: Boolean (default: false)

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "photo_id",
    "fileUrl": "/uploads/photo.jpg",
    "title": "Mountain Sunset",
    "description": "Beautiful sunset over the mountains",
    "tags": ["nature", "sunset", "mountains"],
    "isProfilePhoto": false,
    "uploadDate": "2024-01-01T00:00:00Z",
    "viewCount": 0,
    "likeCount": 0
  }
}
```

### Get Photos Feed
```http
GET /photos/feed
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 50)
- `tags`: Filter by tags (comma-separated)
- `userId`: Filter by user ID
- `sort`: Sort by 'recent', 'popular', 'trending'

**Response (200):**
```json
{
  "success": true,
  "data": {
    "photos": [
      {
        "id": "photo_id",
        "fileUrl": "/uploads/photo.jpg",
        "title": "Mountain Sunset",
        "description": "Beautiful sunset",
        "tags": ["nature", "sunset"],
        "user": {
          "id": "user_id",
          "username": "photographer"
        },
        "uploadDate": "2024-01-01T00:00:00Z",
        "viewCount": 123,
        "likeCount": 45,
        "userInteraction": {
          "liked": false,
          "commented": false
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

### Get Photo by ID
```http
GET /photos/:photoId
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "photo_id",
    "fileUrl": "/uploads/photo.jpg",
    "title": "Mountain Sunset",
    "description": "Beautiful sunset over the mountains",
    "tags": ["nature", "sunset", "mountains"],
    "user": {
      "id": "user_id",
      "username": "photographer",
      "profileData": {
        "name": "John Doe"
      }
    },
    "uploadDate": "2024-01-01T00:00:00Z",
    "viewCount": 123,
    "likeCount": 45,
    "comments": [
      {
        "id": "comment_id",
        "user": {
          "id": "commenter_id",
          "username": "viewer"
        },
        "text": "Amazing shot!",
        "createdAt": "2024-01-01T01:00:00Z"
      }
    ]
  }
}
```

### Update Photo
```http
PUT /photos/:photoId
```

**Request Body:**
```json
{
  "title": "Updated Title",
  "description": "Updated description",
  "tags": ["nature", "sunset", "mountains", "landscape"]
}
```

### Delete Photo
```http
DELETE /photos/:photoId
```

**Response (200):**
```json
{
  "success": true,
  "message": "Photo deleted successfully"
}
```

## 💝 Interactions

### Like Photo
```http
POST /interactions/like
```

**Request Body:**
```json
{
  "photoId": "photo_id"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "liked": true,
    "likeCount": 46,
    "matchTriggered": false
  }
}
```

### Unlike Photo
```http
DELETE /interactions/like/:photoId
```

### Comment on Photo
```http
POST /interactions/comment
```

**Request Body:**
```json
{
  "photoId": "photo_id",
  "text": "Amazing shot! Love the colors."
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "comment_id",
    "text": "Amazing shot! Love the colors.",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### Get User Interactions
```http
GET /interactions/me
```

**Query Parameters:**
- `type`: Filter by 'like', 'comment', 'dislike'
- `page`: Page number
- `limit`: Items per page

## 💕 Matching System

### Get Potential Matches
```http
GET /matches/potential
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "user": {
        "id": "user_id",
        "username": "potential_match",
        "profileData": {
          "name": "Jane Doe",
          "age": 26
        }
      },
      "matchScore": 0.85,
      "commonInterests": ["photography", "travel"],
      "mutualLikes": 15,
      "reason": "You both love nature photography"
    }
  ]
}
```

### Send Match Request
```http
POST /matches/request
```

**Request Body:**
```json
{
  "targetUserId": "user_id",
  "message": "Hi! I love your photography style."
}
```

### Respond to Match Request
```http
PUT /matches/:matchId/respond
```

**Request Body:**
```json
{
  "action": "accept", // or "reject"
  "message": "Thanks! I'd love to chat."
}
```

### Get My Matches
```http
GET /matches/me
```

**Query Parameters:**
- `status`: Filter by 'pending', 'accepted', 'rejected'

## 💬 Messaging

### Send Message
```http
POST /messages
```

**Request Body:**
```json
{
  "recipientId": "user_id",
  "text": "Hello! How are you?",
  "type": "text" // or "photo", "emoji"
}
```

### Get Conversations
```http
GET /messages/conversations
```

### Get Messages in Conversation
```http
GET /messages/conversation/:userId
```

**Query Parameters:**
- `page`: Page number
- `limit`: Messages per page

## 🔧 Admin Endpoints

### Get User Statistics
```http
GET /admin/stats/users
```

**Headers:** `Authorization: Bearer <admin_token>`

### Moderate Content
```http
PUT /admin/moderate/:contentId
```

**Request Body:**
```json
{
  "action": "approve", // or "reject", "flag"
  "reason": "Inappropriate content"
}
```

## ❌ Error Handling

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Valid email required"
      }
    ]
  }
}
```

### Common Error Codes
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (duplicate data)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

### Rate Limiting
- **Authentication**: 5 requests per minute
- **Photo Upload**: 10 uploads per hour
- **Messages**: 100 messages per hour
- **General API**: 1000 requests per hour

---

**API Version**: 1.0.0  
**Last Updated**: December 2024
