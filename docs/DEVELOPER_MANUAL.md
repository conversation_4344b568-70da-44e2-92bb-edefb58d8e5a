# SoloyLibre Developer Manual

## 📋 Table of Contents

1. [Project Architecture](#project-architecture)
2. [Development Environment Setup](#development-environment-setup)
3. [Module Structure](#module-structure)
4. [API Development](#api-development)
5. [Frontend Development](#frontend-development)
6. [Database Management](#database-management)
7. [Security Implementation](#security-implementation)
8. [Testing Guidelines](#testing-guidelines)
9. [Deployment Process](#deployment-process)
10. [Troubleshooting](#troubleshooting)

## 🏗️ Project Architecture

### System Overview
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Express Backend │    │   PostgreSQL    │
│   (Port 3000)   │◄──►│   (Port 8000)   │◄──►│   (Port 5432)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│  Supabase Auth  │◄─────────────┘
                        │  & Storage      │
                        └─────────────────┘
```

### Technology Decisions

**Frontend: React + Vite**
- Fast development with hot reload
- Modern build tooling
- Excellent TypeScript support
- Component-based architecture

**Backend: Node.js + Express**
- JavaScript ecosystem consistency
- Rich middleware ecosystem
- Easy API development
- Good performance for I/O operations

**Database: PostgreSQL + Prisma**
- Robust relational database
- Type-safe database access
- Automatic migrations
- Excellent performance

**Authentication: Supabase**
- Ready-to-use auth system
- Social login integration
- Row-level security
- Real-time subscriptions

## 🛠️ Development Environment Setup

### Prerequisites Installation

```bash
# Install Node.js (version 18+)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### Project Initialization

```bash
# Clone and setup
git clone <repository-url>
cd soloylibre-app

# Create environment files
cp .env.example .env
cp frontend/.env.example frontend/.env
cp backend/.env.example backend/.env

# Install dependencies
npm run install:all

# Setup database
npm run db:setup

# Start development
npm run dev
```

### Environment Variables

**Root `.env`:**
```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/soloylibre"
POSTGRES_USER=soloylibre_user
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=soloylibre

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Security
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# External Services
RECAPTCHA_SITE_KEY=your_recaptcha_site_key
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key
```

## 📁 Module Structure

### Frontend Modules (`frontend/src/`)

```
src/
├── components/           # Reusable UI components
│   ├── common/          # Generic components (Button, Modal, etc.)
│   ├── photo/           # Photo-related components
│   ├── user/            # User profile components
│   └── admin/           # Admin panel components
├── pages/               # Route components
│   ├── Home.jsx         # Photo discovery page
│   ├── Profile.jsx      # User profile page
│   ├── Messages.jsx     # Chat interface
│   └── Admin.jsx        # Admin dashboard
├── hooks/               # Custom React hooks
│   ├── useAuth.js       # Authentication hook
│   ├── usePhotos.js     # Photo management hook
│   └── useMatches.js    # Matching system hook
├── services/            # API communication
│   ├── api.js           # Base API client
│   ├── auth.js          # Authentication service
│   ├── photos.js        # Photo service
│   └── users.js         # User service
├── utils/               # Utility functions
│   ├── validation.js    # Form validation
│   ├── formatting.js    # Data formatting
│   └── constants.js     # App constants
└── styles/              # CSS and styling
    ├── globals.css      # Global styles
    ├── components.css   # Component styles
    └── themes.css       # Theme definitions
```

### Backend Modules (`backend/src/`)

```
src/
├── controllers/         # Request handlers
│   ├── authController.js    # Authentication endpoints
│   ├── photoController.js   # Photo management
│   ├── userController.js    # User operations
│   ├── matchController.js   # Matching system
│   └── adminController.js   # Admin operations
├── middleware/          # Express middleware
│   ├── auth.js          # Authentication middleware
│   ├── validation.js    # Input validation
│   ├── rateLimit.js     # Rate limiting
│   └── security.js      # Security headers
├── models/              # Database models (Prisma)
│   ├── User.js          # User model extensions
│   ├── Photo.js         # Photo model extensions
│   └── Match.js         # Match model extensions
├── services/            # Business logic
│   ├── authService.js   # Authentication logic
│   ├── photoService.js  # Photo processing
│   ├── matchService.js  # Matching algorithm
│   └── notificationService.js # Notifications
├── utils/               # Utility functions
│   ├── encryption.js    # Data encryption
│   ├── validation.js    # Server-side validation
│   └── logger.js        # Logging utility
└── routes/              # API routes
    ├── auth.js          # Authentication routes
    ├── photos.js        # Photo routes
    ├── users.js         # User routes
    └── admin.js         # Admin routes
```

## 🔧 Module Development Guidelines

### Creating New Components

**Frontend Component Template:**
```jsx
// components/common/NewComponent.jsx
import React from 'react';
import PropTypes from 'prop-types';
import './NewComponent.css';

const NewComponent = ({ prop1, prop2, onAction }) => {
  return (
    <div className="new-component">
      {/* Component content */}
    </div>
  );
};

NewComponent.propTypes = {
  prop1: PropTypes.string.required,
  prop2: PropTypes.number,
  onAction: PropTypes.func.required
};

NewComponent.defaultProps = {
  prop2: 0
};

export default NewComponent;
```

**Backend Controller Template:**
```javascript
// controllers/newController.js
const { validationResult } = require('express-validator');
const NewService = require('../services/newService');
const logger = require('../utils/logger');

class NewController {
  async createItem(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const result = await NewService.createItem(req.body);
      res.status(201).json(result);
    } catch (error) {
      logger.error('Error creating item:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}

module.exports = new NewController();
```

### Adding New API Endpoints

1. **Define Route** (`routes/newRoute.js`):
```javascript
const express = require('express');
const { body } = require('express-validator');
const auth = require('../middleware/auth');
const NewController = require('../controllers/newController');

const router = express.Router();

router.post('/items',
  auth.requireAuth,
  [
    body('name').isLength({ min: 1 }).trim(),
    body('description').optional().isLength({ max: 500 })
  ],
  NewController.createItem
);

module.exports = router;
```

2. **Register Route** (`app.js`):
```javascript
app.use('/api/new', require('./routes/newRoute'));
```

3. **Add Frontend Service** (`services/newService.js`):
```javascript
import api from './api';

export const newService = {
  async createItem(data) {
    const response = await api.post('/new/items', data);
    return response.data;
  }
};
```

## 🗄️ Database Management

### Schema Design

**Core Tables:**
```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  profile_data JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP
);

-- Photos table
CREATE TABLE photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  file_url VARCHAR(500) NOT NULL,
  title VARCHAR(200),
  description TEXT,
  tags TEXT[],
  is_profile_photo BOOLEAN DEFAULT false,
  upload_date TIMESTAMP DEFAULT NOW(),
  view_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0
);

-- Photo interactions table
CREATE TABLE photo_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  photo_id UUID REFERENCES photos(id) ON DELETE CASCADE,
  interaction_type VARCHAR(20) NOT NULL, -- 'like', 'dislike', 'comment'
  comment_text TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, photo_id, interaction_type)
);

-- Matches table
CREATE TABLE matches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user1_id UUID REFERENCES users(id) ON DELETE CASCADE,
  user2_id UUID REFERENCES users(id) ON DELETE CASCADE,
  match_score DECIMAL(3,2),
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'rejected'
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user1_id, user2_id)
);
```

### Prisma Schema (`database/schema.prisma`)

```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  username      String   @unique
  passwordHash  String   @map("password_hash")
  profileData   Json?    @map("profile_data")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  isActive      Boolean  @default(true) @map("is_active")
  lastLogin     DateTime? @map("last_login")

  photos        Photo[]
  interactions  PhotoInteraction[]
  matchesAsUser1 Match[] @relation("User1Matches")
  matchesAsUser2 Match[] @relation("User2Matches")

  @@map("users")
}

model Photo {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  fileUrl         String   @map("file_url")
  title           String?
  description     String?
  tags            String[]
  isProfilePhoto  Boolean  @default(false) @map("is_profile_photo")
  uploadDate      DateTime @default(now()) @map("upload_date")
  viewCount       Int      @default(0) @map("view_count")
  likeCount       Int      @default(0) @map("like_count")

  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  interactions    PhotoInteraction[]

  @@map("photos")
}

model PhotoInteraction {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  photoId         String   @map("photo_id")
  interactionType String   @map("interaction_type")
  commentText     String?  @map("comment_text")
  createdAt       DateTime @default(now()) @map("created_at")

  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  photo           Photo    @relation(fields: [photoId], references: [id], onDelete: Cascade)

  @@unique([userId, photoId, interactionType])
  @@map("photo_interactions")
}

model Match {
  id          String   @id @default(cuid())
  user1Id     String   @map("user1_id")
  user2Id     String   @map("user2_id")
  matchScore  Decimal  @map("match_score")
  status      String   @default("pending")
  createdAt   DateTime @default(now()) @map("created_at")

  user1       User     @relation("User1Matches", fields: [user1Id], references: [id], onDelete: Cascade)
  user2       User     @relation("User2Matches", fields: [user2Id], references: [id], onDelete: Cascade)

  @@unique([user1Id, user2Id])
  @@map("matches")
}
```

### Database Operations

**Migration Commands:**
```bash
# Generate migration
npx prisma migrate dev --name migration_name

# Apply migrations
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset

# Generate Prisma client
npx prisma generate
```

**Seeding Data:**
```javascript
// database/seed.js
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  // Create test users
  const hashedPassword = await bcrypt.hash('password123', 10);

  const user1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'user1',
      passwordHash: hashedPassword,
      profileData: {
        name: 'John Doe',
        age: 28,
        interests: ['photography', 'travel', 'nature']
      }
    }
  });

  // Create sample photos
  await prisma.photo.createMany({
    data: [
      {
        userId: user1.id,
        fileUrl: '/uploads/sample1.jpg',
        title: 'Mountain Sunset',
        description: 'Beautiful sunset over the mountains',
        tags: ['nature', 'sunset', 'mountains']
      }
    ]
  });
}

main()
  .catch(console.error)
  .finally(() => prisma.$disconnect());
```

## 🔒 Security Implementation

### Authentication Flow

1. **User Registration:**
```javascript
// services/authService.js
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class AuthService {
  async register(userData) {
    const { email, username, password } = userData;

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        username,
        passwordHash,
        profileData: {}
      }
    });

    // Generate tokens
    const accessToken = this.generateAccessToken(user.id);
    const refreshToken = this.generateRefreshToken(user.id);

    return { user, accessToken, refreshToken };
  }

  generateAccessToken(userId) {
    return jwt.sign(
      { userId, type: 'access' },
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
    );
  }

  generateRefreshToken(userId) {
    return jwt.sign(
      { userId, type: 'refresh' },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
  }
}
```

2. **Authentication Middleware:**
```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const requireAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'Access denied' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
};

module.exports = { requireAuth };
```

### Input Validation

```javascript
// middleware/validation.js
const { body, validationResult } = require('express-validator');

const validateRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email required'),
  body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters, alphanumeric and underscore only'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must be 8+ characters with uppercase, lowercase, number, and special character')
];

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

module.exports = { validateRegistration, handleValidationErrors };
```
```
