# SoloyLibre Deployment Guide

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Local Development](#local-development)
4. [Production Deployment](#production-deployment)
5. [Docker Deployment](#docker-deployment)
6. [Database Setup](#database-setup)
7. [SSL Configuration](#ssl-configuration)
8. [Monitoring & Logging](#monitoring--logging)
9. [Backup & Recovery](#backup--recovery)
10. [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### System Requirements

**Minimum Requirements:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 50GB SSD
- OS: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+

**Recommended for Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 100GB+ SSD
- OS: Ubuntu 22.04 LTS

### Software Dependencies

```bash
# Node.js (version 18+)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Docker & Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# PostgreSQL Client (for database management)
sudo apt-get install postgresql-client

# Nginx (for reverse proxy)
sudo apt-get install nginx

# Certbot (for SSL certificates)
sudo apt-get install certbot python3-certbot-nginx
```

## 🌍 Environment Setup

### 1. Clone Repository

```bash
git clone https://github.com/your-org/soloylibre-app.git
cd soloylibre-app
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env
cp frontend/.env.example frontend/.env
cp backend/.env.example backend/.env

# Edit environment files with your values
nano .env
```

### 3. Required External Services

**Supabase Setup:**
1. Create account at [supabase.com](https://supabase.com)
2. Create new project
3. Get API URL and keys from Settings > API
4. Configure authentication providers
5. Set up storage buckets for photos

**reCAPTCHA Setup:**
1. Go to [Google reCAPTCHA](https://www.google.com/recaptcha)
2. Register your domain
3. Get site key and secret key
4. Add to environment variables

## 🏠 Local Development

### Quick Start

```bash
# Install all dependencies
npm run install:all

# Setup database
npm run db:setup

# Start development servers
npm run dev
```

**Access Points:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- Database: localhost:5432

### Development Commands

```bash
# Start individual services
npm run dev:frontend    # React development server
npm run dev:backend     # Node.js API server

# Database operations
npm run db:migrate      # Run database migrations
npm run db:seed         # Seed database with test data
npm run db:reset        # Reset database (development only)

# Testing
npm run test           # Run all tests
npm run test:frontend  # Frontend tests only
npm run test:backend   # Backend tests only

# Code quality
npm run lint           # Lint all code
npm run format         # Format all code
npm run security:audit # Security audit
```

## 🚀 Production Deployment

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y curl wget git nginx postgresql-client

# Create application user
sudo useradd -m -s /bin/bash soloylibre
sudo usermod -aG docker soloylibre

# Create application directory
sudo mkdir -p /opt/soloylibre
sudo chown soloylibre:soloylibre /opt/soloylibre
```

### 2. Application Deployment

```bash
# Switch to application user
sudo su - soloylibre

# Clone repository
cd /opt/soloylibre
git clone https://github.com/your-org/soloylibre-app.git .

# Configure environment
cp .env.example .env
# Edit .env with production values

# Build application
npm run install:all
npm run build
```

### 3. Process Management with PM2

```bash
# Install PM2
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'soloylibre-backend',
      script: './backend/dist/index.js',
      cwd: '/opt/soloylibre',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 8000
      },
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true
    }
  ]
};
EOF

# Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 🐳 Docker Deployment

### Development with Docker

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Docker Deployment

```bash
# Create production docker-compose override
cat > docker-compose.prod.yml << EOF
version: '3.8'

services:
  backend:
    environment:
      NODE_ENV: production
    restart: always
    
  frontend:
    environment:
      NODE_ENV: production
    restart: always
    
  nginx:
    ports:
      - "80:80"
      - "443:443"
    restart: always
EOF

# Deploy with production configuration
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Docker Health Checks

```bash
# Check service health
docker-compose ps

# View service logs
docker-compose logs backend
docker-compose logs frontend
docker-compose logs database

# Execute commands in containers
docker-compose exec backend npm run db:migrate
docker-compose exec database psql -U soloylibre_user -d soloylibre
```

## 🗄️ Database Setup

### PostgreSQL Installation

```bash
# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql << EOF
CREATE DATABASE soloylibre;
CREATE USER soloylibre_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE soloylibre TO soloylibre_user;
ALTER USER soloylibre_user CREATEDB;
\q
EOF
```

### Database Migration

```bash
# Run migrations
cd backend
npx prisma migrate deploy

# Seed database
npx prisma db seed

# Generate Prisma client
npx prisma generate
```

### Database Backup

```bash
# Create backup script
cat > /opt/soloylibre/scripts/backup-db.sh << EOF
#!/bin/bash
BACKUP_DIR="/opt/soloylibre/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="soloylibre_backup_\$DATE.sql"

mkdir -p \$BACKUP_DIR
pg_dump -h localhost -U soloylibre_user -d soloylibre > \$BACKUP_DIR/\$BACKUP_FILE
gzip \$BACKUP_DIR/\$BACKUP_FILE

# Keep only last 30 days of backups
find \$BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
EOF

chmod +x /opt/soloylibre/scripts/backup-db.sh

# Schedule daily backups
echo "0 2 * * * /opt/soloylibre/scripts/backup-db.sh" | crontab -
```

## 🔒 SSL Configuration

### Let's Encrypt with Certbot

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test automatic renewal
sudo certbot renew --dry-run

# Setup automatic renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### Nginx Configuration

```nginx
# /etc/nginx/sites-available/soloylibre
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files
    location /uploads {
        alias /opt/soloylibre/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 📊 Monitoring & Logging

### Application Monitoring

```bash
# Install monitoring tools
npm install -g pm2-logrotate

# Configure log rotation
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

### System Monitoring

```bash
# Install system monitoring
sudo apt install htop iotop nethogs

# Setup log monitoring
sudo apt install logwatch
sudo logwatch --detail Med --mailto <EMAIL> --service All --range today
```

### Health Check Endpoints

The application provides health check endpoints:
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed system status
- `GET /metrics` - Prometheus metrics (if enabled)

## 🔄 Backup & Recovery

### Automated Backup Script

```bash
#!/bin/bash
# /opt/soloylibre/scripts/full-backup.sh

BACKUP_DIR="/opt/soloylibre/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="soloylibre_full_backup_$DATE"

# Create backup directory
mkdir -p $BACKUP_DIR/$BACKUP_NAME

# Database backup
pg_dump -h localhost -U soloylibre_user -d soloylibre > $BACKUP_DIR/$BACKUP_NAME/database.sql

# Application files backup
tar -czf $BACKUP_DIR/$BACKUP_NAME/uploads.tar.gz /opt/soloylibre/uploads
cp /opt/soloylibre/.env $BACKUP_DIR/$BACKUP_NAME/

# Create archive
cd $BACKUP_DIR
tar -czf $BACKUP_NAME.tar.gz $BACKUP_NAME/
rm -rf $BACKUP_NAME/

# Upload to remote storage (optional)
# aws s3 cp $BACKUP_NAME.tar.gz s3://your-backup-bucket/

# Cleanup old backups
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### Recovery Process

```bash
# Stop application
pm2 stop all

# Restore database
psql -h localhost -U soloylibre_user -d soloylibre < backup/database.sql

# Restore uploads
tar -xzf backup/uploads.tar.gz -C /

# Restore configuration
cp backup/.env /opt/soloylibre/

# Start application
pm2 start all
```

## 🔧 Troubleshooting

### Common Issues

**Database Connection Issues:**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connectivity
psql -h localhost -U soloylibre_user -d soloylibre -c "SELECT 1;"

# View database logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

**Application Issues:**
```bash
# Check application logs
pm2 logs soloylibre-backend

# Check system resources
htop
df -h
free -m

# Check network connectivity
netstat -tlnp | grep :8000
curl -I http://localhost:8000/health
```

**Docker Issues:**
```bash
# Check container status
docker-compose ps

# View container logs
docker-compose logs backend

# Restart services
docker-compose restart backend

# Rebuild containers
docker-compose build --no-cache
```

### Performance Optimization

**Database Optimization:**
```sql
-- Add indexes for better performance
CREATE INDEX idx_photos_user_id ON photos(user_id);
CREATE INDEX idx_photos_tags ON photos USING GIN(tags);
CREATE INDEX idx_photo_interactions_user_photo ON photo_interactions(user_id, photo_id);
CREATE INDEX idx_matches_users ON matches(user1_id, user2_id);
```

**Application Optimization:**
```bash
# Enable gzip compression in Nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Configure caching
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

---

**Deployment Version**: 1.0.0  
**Last Updated**: December 2024  
**Support**: <EMAIL>
