# SoloyLibre Security Guide

## 🛡️ Table of Contents

1. [Security Overview](#security-overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Data Protection](#data-protection)
4. [Input Validation](#input-validation)
5. [Rate Limiting](#rate-limiting)
6. [File Upload Security](#file-upload-security)
7. [Database Security](#database-security)
8. [Network Security](#network-security)
9. [Monitoring & Logging](#monitoring--logging)
10. [Incident Response](#incident-response)

## 🔒 Security Overview

SoloyLibre implements multiple layers of security to protect user data and prevent common web application vulnerabilities. This guide outlines our security measures and best practices.

### Security Principles

1. **Defense in Depth**: Multiple security layers
2. **Least Privilege**: Minimal necessary permissions
3. **Zero Trust**: Verify everything, trust nothing
4. **Privacy by Design**: Data protection built-in
5. **Continuous Monitoring**: Real-time threat detection

### Compliance Standards

- **OWASP Top 10**: Protection against common vulnerabilities
- **GDPR**: European data protection compliance
- **CCPA**: California privacy rights compliance
- **SOC 2**: Security and availability controls

## 🔐 Authentication & Authorization

### Multi-Factor Authentication

```javascript
// JWT Token Structure
{
  "userId": "user_id",
  "email": "<EMAIL>",
  "role": "user",
  "permissions": ["read:photos", "write:photos"],
  "iat": 1640995200,
  "exp": 1640998800,
  "type": "access"
}
```

### Password Security

**Password Requirements:**
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character
- Cannot contain username or email

**Implementation:**
```javascript
// Password hashing with bcrypt
const bcrypt = require('bcrypt');
const SALT_ROUNDS = 12;

async function hashPassword(password) {
  return await bcrypt.hash(password, SALT_ROUNDS);
}

async function verifyPassword(password, hash) {
  return await bcrypt.compare(password, hash);
}
```

### Session Management

```javascript
// JWT Configuration
const jwtConfig = {
  accessToken: {
    secret: process.env.JWT_SECRET,
    expiresIn: '15m'
  },
  refreshToken: {
    secret: process.env.JWT_REFRESH_SECRET,
    expiresIn: '7d'
  }
};

// Token blacklisting for logout
const tokenBlacklist = new Set();

function isTokenBlacklisted(token) {
  return tokenBlacklist.has(token);
}
```

### Role-Based Access Control (RBAC)

```javascript
// User roles and permissions
const roles = {
  user: ['read:photos', 'write:own_photos', 'read:own_profile'],
  moderator: ['read:photos', 'moderate:content', 'read:reports'],
  admin: ['*'] // All permissions
};

// Permission middleware
function requirePermission(permission) {
  return (req, res, next) => {
    if (!req.user.permissions.includes(permission) && 
        !req.user.permissions.includes('*')) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
}
```

## 🔐 Data Protection

### Encryption at Rest

```javascript
// Sensitive data encryption
const crypto = require('crypto');

class DataEncryption {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.key = Buffer.from(process.env.ENCRYPTION_KEY, 'hex');
  }

  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  decrypt(encryptedData) {
    const decipher = crypto.createDecipher(
      this.algorithm, 
      this.key, 
      Buffer.from(encryptedData.iv, 'hex')
    );
    
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

### Data Anonymization

```javascript
// Personal data anonymization
function anonymizeUser(user) {
  return {
    id: user.id,
    username: `user_${crypto.randomBytes(4).toString('hex')}`,
    email: '<EMAIL>',
    profileData: {
      ...user.profileData,
      name: 'Anonymous User',
      location: null,
      phone: null
    },
    createdAt: user.createdAt,
    isAnonymized: true
  };
}
```

### GDPR Compliance

```javascript
// Data export for GDPR requests
async function exportUserData(userId) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      photos: true,
      interactions: true,
      matches: true
    }
  });

  return {
    personalData: user,
    exportDate: new Date().toISOString(),
    dataRetentionPolicy: '2 years from last activity'
  };
}

// Data deletion for GDPR requests
async function deleteUserData(userId) {
  await prisma.$transaction([
    prisma.photoInteraction.deleteMany({ where: { userId } }),
    prisma.match.deleteMany({ 
      where: { 
        OR: [{ user1Id: userId }, { user2Id: userId }] 
      } 
    }),
    prisma.photo.deleteMany({ where: { userId } }),
    prisma.user.delete({ where: { id: userId } })
  ]);
}
```

## ✅ Input Validation

### Server-Side Validation

```javascript
// Express-validator middleware
const { body, param, query, validationResult } = require('express-validator');

// User registration validation
const validateRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .custom(async (email) => {
      const user = await prisma.user.findUnique({ where: { email } });
      if (user) throw new Error('Email already exists');
    }),
  body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .custom(async (username) => {
      const user = await prisma.user.findUnique({ where: { username } });
      if (user) throw new Error('Username already exists');
    }),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
];

// Photo upload validation
const validatePhotoUpload = [
  body('title')
    .optional()
    .isLength({ max: 200 })
    .trim()
    .escape(),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .trim()
    .escape(),
  body('tags')
    .isArray({ max: 10 })
    .custom((tags) => {
      return tags.every(tag => 
        typeof tag === 'string' && 
        tag.length <= 50 && 
        /^[a-zA-Z0-9\s]+$/.test(tag)
      );
    })
];
```

### XSS Prevention

```javascript
// Content Security Policy
const helmet = require('helmet');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: []
    }
  }
}));

// HTML sanitization
const DOMPurify = require('isomorphic-dompurify');

function sanitizeHtml(html) {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: []
  });
}
```

### SQL Injection Prevention

```javascript
// Using Prisma ORM prevents SQL injection
// Raw queries should use parameterized statements
async function safeRawQuery(userId) {
  return await prisma.$queryRaw`
    SELECT * FROM users 
    WHERE id = ${userId} 
    AND is_active = true
  `;
}
```

## 🚦 Rate Limiting

### API Rate Limiting

```javascript
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const Redis = require('ioredis');

const redis = new Redis(process.env.REDIS_URL);

// General API rate limiting
const generalLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP',
  standardHeaders: true,
  legacyHeaders: false,
});

// Authentication rate limiting
const authLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000,
  max: 5, // limit each IP to 5 login attempts per windowMs
  skipSuccessfulRequests: true,
});

// Photo upload rate limiting
const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // limit each user to 10 uploads per hour
  keyGenerator: (req) => req.user.id,
});
```

### Brute Force Protection

```javascript
// Account lockout after failed attempts
class BruteForceProtection {
  constructor() {
    this.attempts = new Map();
    this.lockouts = new Map();
  }

  async checkAttempts(identifier) {
    const attempts = this.attempts.get(identifier) || 0;
    const lockoutTime = this.lockouts.get(identifier);

    if (lockoutTime && Date.now() < lockoutTime) {
      throw new Error('Account temporarily locked');
    }

    if (attempts >= 5) {
      this.lockouts.set(identifier, Date.now() + 15 * 60 * 1000); // 15 min lockout
      this.attempts.delete(identifier);
      throw new Error('Too many failed attempts');
    }
  }

  recordFailedAttempt(identifier) {
    const attempts = this.attempts.get(identifier) || 0;
    this.attempts.set(identifier, attempts + 1);
  }

  recordSuccessfulAttempt(identifier) {
    this.attempts.delete(identifier);
    this.lockouts.delete(identifier);
  }
}
```

## 📁 File Upload Security

### File Type Validation

```javascript
const multer = require('multer');
const path = require('path');
const crypto = require('crypto');

// File filter
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type'), false);
  }
};

// Storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/photos/');
  },
  filename: (req, file, cb) => {
    const uniqueName = crypto.randomBytes(16).toString('hex');
    const extension = path.extname(file.originalname);
    cb(null, `${uniqueName}${extension}`);
  }
});

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  }
});
```

### Image Processing & Sanitization

```javascript
const sharp = require('sharp');

async function processAndSanitizeImage(inputPath, outputPath) {
  try {
    await sharp(inputPath)
      .resize(1920, 1920, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ 
        quality: 85,
        progressive: true 
      })
      .withMetadata(false) // Remove EXIF data
      .toFile(outputPath);
      
    // Delete original file
    fs.unlinkSync(inputPath);
    
    return outputPath;
  } catch (error) {
    throw new Error('Image processing failed');
  }
}
```

## 🗄️ Database Security

### Connection Security

```javascript
// Database connection with SSL
const databaseConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false,
    ca: fs.readFileSync('path/to/ca-certificate.crt').toString(),
  } : false,
  logging: process.env.NODE_ENV === 'development'
};
```

### Database Auditing

```sql
-- Audit table for tracking changes
CREATE TABLE audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name VARCHAR(50) NOT NULL,
  operation VARCHAR(10) NOT NULL,
  old_values JSONB,
  new_values JSONB,
  user_id UUID,
  timestamp TIMESTAMP DEFAULT NOW(),
  ip_address INET
);

-- Trigger function for auditing
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO audit_log (table_name, operation, new_values, user_id)
    VALUES (TG_TABLE_NAME, TG_OP, row_to_json(NEW), NEW.user_id);
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_log (table_name, operation, old_values, new_values, user_id)
    VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), row_to_json(NEW), NEW.user_id);
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO audit_log (table_name, operation, old_values, user_id)
    VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), OLD.user_id);
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;
```

## 🌐 Network Security

### HTTPS Configuration

```nginx
# SSL/TLS configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;

# HSTS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Security headers
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

### CORS Configuration

```javascript
const cors = require('cors');

const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'https://soloylibre.com',
      'https://www.soloylibre.com'
    ];
    
    if (process.env.NODE_ENV === 'development') {
      allowedOrigins.push('http://localhost:3000');
    }
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
```

## 📊 Monitoring & Logging

### Security Event Logging

```javascript
const winston = require('winston');

const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/security.log' }),
    new winston.transports.Console()
  ]
});

// Log security events
function logSecurityEvent(event, details, req) {
  securityLogger.warn({
    event,
    details,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  });
}

// Usage examples
logSecurityEvent('FAILED_LOGIN', { email: '<EMAIL>' }, req);
logSecurityEvent('SUSPICIOUS_ACTIVITY', { reason: 'Multiple rapid requests' }, req);
logSecurityEvent('UNAUTHORIZED_ACCESS', { resource: '/admin/users' }, req);
```

### Intrusion Detection

```javascript
// Suspicious activity detection
class IntrusionDetection {
  constructor() {
    this.suspiciousPatterns = [
      /(\bor\b|\band\b).*=.*\d/i, // SQL injection patterns
      /<script[^>]*>.*?<\/script>/i, // XSS patterns
      /\.\.\//g, // Directory traversal
      /eval\s*\(/i, // Code injection
    ];
  }

  checkRequest(req) {
    const suspicious = [];
    
    // Check URL
    if (this.containsSuspiciousPattern(req.url)) {
      suspicious.push('URL contains suspicious pattern');
    }
    
    // Check headers
    Object.entries(req.headers).forEach(([key, value]) => {
      if (this.containsSuspiciousPattern(value)) {
        suspicious.push(`Header ${key} contains suspicious pattern`);
      }
    });
    
    // Check body
    if (req.body && this.containsSuspiciousPattern(JSON.stringify(req.body))) {
      suspicious.push('Request body contains suspicious pattern');
    }
    
    if (suspicious.length > 0) {
      logSecurityEvent('SUSPICIOUS_REQUEST', { patterns: suspicious }, req);
      return false;
    }
    
    return true;
  }

  containsSuspiciousPattern(text) {
    return this.suspiciousPatterns.some(pattern => pattern.test(text));
  }
}
```

## 🚨 Incident Response

### Security Incident Workflow

1. **Detection**: Automated monitoring alerts
2. **Assessment**: Determine severity and impact
3. **Containment**: Isolate affected systems
4. **Eradication**: Remove threat and vulnerabilities
5. **Recovery**: Restore normal operations
6. **Lessons Learned**: Document and improve

### Incident Response Procedures

```javascript
// Emergency response functions
class IncidentResponse {
  async lockUserAccount(userId, reason) {
    await prisma.user.update({
      where: { id: userId },
      data: { 
        isActive: false,
        lockReason: reason,
        lockedAt: new Date()
      }
    });
    
    logSecurityEvent('ACCOUNT_LOCKED', { userId, reason });
  }

  async blockIP(ipAddress, reason) {
    // Add to firewall block list
    await this.addFirewallRule(ipAddress, 'DENY');
    
    logSecurityEvent('IP_BLOCKED', { ipAddress, reason });
  }

  async emergencyShutdown() {
    // Gracefully shutdown application
    logSecurityEvent('EMERGENCY_SHUTDOWN', { reason: 'Security incident' });
    process.exit(1);
  }
}
```

### Contact Information

**Security Team:**
- Email: <EMAIL>
- Emergency: +1-XXX-XXX-XXXX
- PGP Key: [Public key for encrypted communications]

**Vulnerability Disclosure:**
- Report security issues to: <EMAIL>
- Response time: 24 hours for critical issues
- Bug bounty program: Available for verified vulnerabilities

---

**Security Version**: 1.0.0  
**Last Updated**: December 2024  
**Next Review**: March 2025
