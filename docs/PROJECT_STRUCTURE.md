# SoloyLibre Project Structure Guide

## 📁 Complete Directory Structure

```
soloylibre-app/
├── 📄 README.md                     # Main project documentation
├── 📄 package.json                  # Root package configuration
├── 📄 docker-compose.yml            # Multi-container orchestration
├── 📄 .env.example                  # Environment variables template
├── 📄 .gitignore                    # Git ignore rules
├── 📄 LICENSE                       # Project license
│
├── 📁 frontend/                     # React frontend application
│   ├── 📄 package.json              # Frontend dependencies
│   ├── 📄 vite.config.js            # Vite configuration
│   ├── 📄 index.html                # HTML template
│   ├── 📄 .env.example              # Frontend environment template
│   ├── 📄 Dockerfile                # Frontend container config
│   │
│   ├── 📁 src/                      # Source code
│   │   ├── 📄 main.jsx               # Application entry point
│   │   ├── 📄 App.jsx                # Main App component
│   │   │
│   │   ├── 📁 components/            # Reusable UI components
│   │   │   ├── 📁 common/            # Generic components
│   │   │   │   ├── 📄 Button.jsx
│   │   │   │   ├── 📄 Modal.jsx
│   │   │   │   ├── 📄 Loading.jsx
│   │   │   │   └── 📄 ErrorBoundary.jsx
│   │   │   │
│   │   │   ├── 📁 photo/             # Photo-related components
│   │   │   │   ├── 📄 PhotoCard.jsx
│   │   │   │   ├── 📄 PhotoGallery.jsx
│   │   │   │   ├── 📄 PhotoUpload.jsx
│   │   │   │   └── 📄 PhotoSwiper.jsx
│   │   │   │
│   │   │   ├── 📁 user/              # User profile components
│   │   │   │   ├── 📄 ProfileCard.jsx
│   │   │   │   ├── 📄 ProfileEdit.jsx
│   │   │   │   └── 📄 UserStats.jsx
│   │   │   │
│   │   │   ├── 📁 auth/              # Authentication components
│   │   │   │   ├── 📄 LoginForm.jsx
│   │   │   │   ├── 📄 RegisterForm.jsx
│   │   │   │   └── 📄 ProtectedRoute.jsx
│   │   │   │
│   │   │   ├── 📁 messaging/         # Chat components
│   │   │   │   ├── 📄 ChatWindow.jsx
│   │   │   │   ├── 📄 MessageList.jsx
│   │   │   │   └── 📄 ConversationList.jsx
│   │   │   │
│   │   │   └── 📁 admin/             # Admin panel components
│   │   │       ├── 📄 Dashboard.jsx
│   │   │       ├── 📄 UserManagement.jsx
│   │   │       ├── 📄 ContentModeration.jsx
│   │   │       └── 📄 Analytics.jsx
│   │   │
│   │   ├── 📁 pages/                 # Route components
│   │   │   ├── 📄 Home.jsx           # Photo discovery page
│   │   │   ├── 📄 Profile.jsx        # User profile page
│   │   │   ├── 📄 Messages.jsx       # Chat interface
│   │   │   ├── 📄 Matches.jsx        # Matches page
│   │   │   ├── 📄 Settings.jsx       # User settings
│   │   │   ├── 📄 Admin.jsx          # Admin dashboard
│   │   │   ├── 📄 Login.jsx          # Login page
│   │   │   ├── 📄 Register.jsx       # Registration page
│   │   │   └── 📄 NotFound.jsx       # 404 page
│   │   │
│   │   ├── 📁 hooks/                 # Custom React hooks
│   │   │   ├── 📄 useAuth.js         # Authentication hook
│   │   │   ├── 📄 usePhotos.js       # Photo management hook
│   │   │   ├── 📄 useMatches.js      # Matching system hook
│   │   │   ├── 📄 useMessages.js     # Messaging hook
│   │   │   └── 📄 useLocalStorage.js # Local storage hook
│   │   │
│   │   ├── 📁 services/              # API communication
│   │   │   ├── 📄 api.js             # Base API client
│   │   │   ├── 📄 auth.js            # Authentication service
│   │   │   ├── 📄 photos.js          # Photo service
│   │   │   ├── 📄 users.js           # User service
│   │   │   ├── 📄 matches.js         # Matching service
│   │   │   └── 📄 messages.js        # Messaging service
│   │   │
│   │   ├── 📁 utils/                 # Utility functions
│   │   │   ├── 📄 validation.js      # Form validation
│   │   │   ├── 📄 formatting.js      # Data formatting
│   │   │   ├── 📄 constants.js       # App constants
│   │   │   ├── 📄 helpers.js         # Helper functions
│   │   │   └── 📄 storage.js         # Storage utilities
│   │   │
│   │   ├── 📁 context/               # React context providers
│   │   │   ├── 📄 AuthContext.jsx    # Authentication context
│   │   │   ├── 📄 ThemeContext.jsx   # Theme context
│   │   │   └── 📄 NotificationContext.jsx # Notifications
│   │   │
│   │   └── 📁 styles/                # CSS and styling
│   │       ├── 📄 globals.css        # Global styles
│   │       ├── 📄 components.css     # Component styles
│   │       ├── 📄 themes.css         # Theme definitions
│   │       └── 📄 responsive.css     # Responsive design
│   │
│   ├── 📁 public/                    # Static assets
│   │   ├── 📄 favicon.ico
│   │   ├── 📄 manifest.json
│   │   └── 📁 images/
│   │
│   └── 📁 tests/                     # Frontend tests
│       ├── 📄 setup.js
│       ├── 📁 components/
│       ├── 📁 pages/
│       └── 📁 utils/
│
├── 📁 backend/                       # Node.js API server
│   ├── 📄 package.json               # Backend dependencies
│   ├── 📄 .env.example               # Backend environment template
│   ├── 📄 Dockerfile                 # Backend container config
│   ├── 📄 server.js                  # Server entry point
│   │
│   ├── 📁 src/                       # Source code
│   │   ├── 📄 app.js                 # Express app configuration
│   │   ├── 📄 index.js               # Application entry point
│   │   │
│   │   ├── 📁 controllers/           # Request handlers
│   │   │   ├── 📄 authController.js  # Authentication endpoints
│   │   │   ├── 📄 photoController.js # Photo management
│   │   │   ├── 📄 userController.js  # User operations
│   │   │   ├── 📄 matchController.js # Matching system
│   │   │   ├── 📄 messageController.js # Messaging
│   │   │   └── 📄 adminController.js # Admin operations
│   │   │
│   │   ├── 📁 middleware/            # Express middleware
│   │   │   ├── 📄 auth.js            # Authentication middleware
│   │   │   ├── 📄 validation.js      # Input validation
│   │   │   ├── 📄 rateLimit.js       # Rate limiting
│   │   │   ├── 📄 security.js        # Security headers
│   │   │   ├── 📄 upload.js          # File upload handling
│   │   │   └── 📄 errorHandler.js    # Error handling
│   │   │
│   │   ├── 📁 models/                # Database models (Prisma)
│   │   │   ├── 📄 User.js            # User model extensions
│   │   │   ├── 📄 Photo.js           # Photo model extensions
│   │   │   ├── 📄 Match.js           # Match model extensions
│   │   │   └── 📄 Message.js         # Message model extensions
│   │   │
│   │   ├── 📁 services/              # Business logic
│   │   │   ├── 📄 authService.js     # Authentication logic
│   │   │   ├── 📄 photoService.js    # Photo processing
│   │   │   ├── 📄 matchService.js    # Matching algorithm
│   │   │   ├── 📄 messageService.js  # Messaging logic
│   │   │   ├── 📄 notificationService.js # Notifications
│   │   │   └── 📄 emailService.js    # Email sending
│   │   │
│   │   ├── 📁 utils/                 # Utility functions
│   │   │   ├── 📄 encryption.js      # Data encryption
│   │   │   ├── 📄 validation.js      # Server-side validation
│   │   │   ├── 📄 logger.js          # Logging utility
│   │   │   ├── 📄 helpers.js         # Helper functions
│   │   │   └── 📄 constants.js       # Server constants
│   │   │
│   │   ├── 📁 routes/                # API routes
│   │   │   ├── 📄 index.js           # Route aggregator
│   │   │   ├── 📄 auth.js            # Authentication routes
│   │   │   ├── 📄 photos.js          # Photo routes
│   │   │   ├── 📄 users.js           # User routes
│   │   │   ├── 📄 matches.js         # Matching routes
│   │   │   ├── 📄 messages.js        # Messaging routes
│   │   │   └── 📄 admin.js           # Admin routes
│   │   │
│   │   └── 📁 config/                # Configuration files
│   │       ├── 📄 database.js        # Database configuration
│   │       ├── 📄 redis.js           # Redis configuration
│   │       ├── 📄 supabase.js        # Supabase configuration
│   │       └── 📄 email.js           # Email configuration
│   │
│   ├── 📁 uploads/                   # File uploads (development)
│   ├── 📁 logs/                      # Application logs
│   └── 📁 tests/                     # Backend tests
│       ├── 📄 setup.js
│       ├── 📁 controllers/
│       ├── 📁 services/
│       └── 📁 utils/
│
├── 📁 database/                      # Database schemas and migrations
│   ├── 📄 schema.prisma              # Prisma schema definition
│   ├── 📄 seed.js                    # Database seeding script
│   │
│   ├── 📁 migrations/                # Database migrations
│   │   ├── 📄 001_initial_schema.sql
│   │   ├── 📄 002_add_indexes.sql
│   │   └── 📄 003_add_audit_tables.sql
│   │
│   └── 📁 init/                      # Database initialization
│       └── 📄 01-init.sql
│
├── 📁 docker/                        # Docker configurations
│   ├── 📁 nginx/                     # Nginx configuration
│   │   ├── 📄 nginx.conf
│   │   └── 📄 default.conf
│   │
│   ├── 📁 ssl/                       # SSL certificates
│   │   ├── 📄 cert.pem
│   │   └── 📄 key.pem
│   │
│   └── 📁 backup/                    # Backup container
│       ├── 📄 Dockerfile
│       └── 📄 backup-script.sh
│
├── 📁 scripts/                       # Deployment and utility scripts
│   ├── 📄 backup.js                  # Backup script
│   ├── 📄 restore.js                 # Restore script
│   ├── 📄 deploy.sh                  # Deployment script
│   ├── 📄 setup.sh                   # Initial setup script
│   └── 📄 health-check.js            # Health monitoring
│
├── 📁 docs/                          # Documentation and manuals
│   ├── 📄 DEVELOPER_MANUAL.md        # Complete development guide
│   ├── 📄 USER_MANUAL.md             # End-user documentation
│   ├── 📄 API_REFERENCE.md           # Backend API reference
│   ├── 📄 DEPLOYMENT.md              # Production deployment
│   ├── 📄 SECURITY.md                # Security best practices
│   ├── 📄 PROJECT_STRUCTURE.md       # This file
│   └── 📄 CHANGELOG.md               # Version history
│
├── 📁 tests/                         # Integration and E2E tests
│   ├── 📄 jest.config.js
│   ├── 📄 playwright.config.js
│   ├── 📁 integration/
│   ├── 📁 e2e/
│   └── 📁 fixtures/
│
└── 📁 .github/                       # GitHub workflows and templates
    ├── 📁 workflows/
    │   ├── 📄 ci.yml
    │   ├── 📄 deploy.yml
    │   └── 📄 security.yml
    │
    ├── 📁 ISSUE_TEMPLATE/
    └── 📄 PULL_REQUEST_TEMPLATE.md
```

## 🔧 Module Responsibilities

### Frontend Modules

| Module | Purpose | Key Files |
|--------|---------|-----------|
| `components/common/` | Reusable UI components | Button, Modal, Loading |
| `components/photo/` | Photo-related functionality | PhotoCard, PhotoUpload, PhotoSwiper |
| `components/user/` | User profile management | ProfileCard, ProfileEdit, UserStats |
| `components/auth/` | Authentication UI | LoginForm, RegisterForm, ProtectedRoute |
| `components/messaging/` | Chat interface | ChatWindow, MessageList |
| `components/admin/` | Admin panel | Dashboard, UserManagement, Analytics |
| `pages/` | Route components | Home, Profile, Messages, Settings |
| `hooks/` | Custom React hooks | useAuth, usePhotos, useMatches |
| `services/` | API communication | auth, photos, users, matches |
| `utils/` | Helper functions | validation, formatting, constants |

### Backend Modules

| Module | Purpose | Key Files |
|--------|---------|-----------|
| `controllers/` | HTTP request handlers | authController, photoController |
| `middleware/` | Express middleware | auth, validation, rateLimit |
| `models/` | Database model extensions | User, Photo, Match |
| `services/` | Business logic | authService, matchService |
| `routes/` | API route definitions | auth, photos, users |
| `utils/` | Server utilities | encryption, logger, validation |
| `config/` | Configuration files | database, redis, supabase |

## 📝 File Naming Conventions

### Frontend (React)
- **Components**: PascalCase (e.g., `PhotoCard.jsx`)
- **Hooks**: camelCase with "use" prefix (e.g., `useAuth.js`)
- **Services**: camelCase (e.g., `authService.js`)
- **Utils**: camelCase (e.g., `validation.js`)
- **Pages**: PascalCase (e.g., `Home.jsx`)

### Backend (Node.js)
- **Controllers**: camelCase with "Controller" suffix (e.g., `authController.js`)
- **Services**: camelCase with "Service" suffix (e.g., `authService.js`)
- **Middleware**: camelCase (e.g., `auth.js`)
- **Routes**: camelCase (e.g., `auth.js`)
- **Models**: PascalCase (e.g., `User.js`)

### General
- **Configuration**: lowercase with hyphens (e.g., `docker-compose.yml`)
- **Documentation**: UPPERCASE (e.g., `README.md`)
- **Scripts**: lowercase with hyphens (e.g., `backup.js`)

## 🔄 Data Flow

### Photo Discovery Flow
```
User → PhotoSwiper → PhotoService → API → PhotoController → PhotoService → Database
```

### Authentication Flow
```
User → LoginForm → AuthService → API → AuthController → AuthService → Database → JWT
```

### Matching Flow
```
PhotoInteraction → MatchService → MatchingAlgorithm → Database → NotificationService
```

## 🚀 Quick Reference

### Development Commands
```bash
npm run dev              # Start development servers
npm run build            # Build for production
npm run test             # Run all tests
npm run lint             # Code linting
npm run db:migrate       # Run database migrations
```

### Docker Commands
```bash
docker-compose up -d     # Start all services
docker-compose logs -f   # View logs
docker-compose down      # Stop all services
```

### Backup Commands
```bash
npm run backup           # Create system backup
npm run restore          # Restore from backup
```

---

**Project Structure Version**: 1.0.0  
**Last Updated**: December 2024  
**Maintainer**: Development Team
