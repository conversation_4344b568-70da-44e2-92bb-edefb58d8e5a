# SoloyLibre Quick Start Guide

## 🚀 Get Started in 5 Minutes

This guide will get you up and running with SoloyLibre in just a few minutes.

## 📋 Prerequisites

Before you begin, ensure you have:
- **Node.js 18+** installed
- **Docker & Docker Compose** installed
- **Git** installed
- A **Supabase account** (free tier is fine)
- A **Google reCAPTCHA** account (free)

## ⚡ Quick Setup

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/soloylibre-app.git
cd soloylibre-app
```

### 2. Environment Setup
```bash
# Copy environment templates
cp .env.example .env
cp frontend/.env.example frontend/.env
cp backend/.env.example backend/.env
```

### 3. Configure External Services

**Supabase Setup (2 minutes):**
1. Go to [supabase.com](https://supabase.com) and create a free account
2. Create a new project
3. Go to Settings > API and copy:
   - Project URL
   - Anon public key
   - Service role key
4. Update your `.env` files with these values

**reCAPTCHA Setup (1 minute):**
1. Go to [Google reCAPTCHA](https://www.google.com/recaptcha)
2. Register your site (use `localhost` for development)
3. Copy site key and secret key
4. Update your `.env` files

### 4. Start the Application
```bash
# Install dependencies and start everything
npm run install:all
docker-compose up -d
```

### 5. Access Your Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Database**: localhost:5432

## 🎯 What You Get

After setup, you'll have:
- ✅ Full-stack photo-based dating platform
- ✅ User registration and authentication
- ✅ Photo upload and discovery system
- ✅ Swipe-based interaction
- ✅ Matching algorithm
- ✅ Real-time messaging
- ✅ Admin panel
- ✅ Automated backups
- ✅ Security features

## 🔧 Development Workflow

### Daily Development
```bash
# Start development servers
npm run dev

# Run tests
npm run test

# Check code quality
npm run lint
npm run format
```

### Database Operations
```bash
# Run migrations
npm run db:migrate

# Seed with test data
npm run db:seed

# Reset database (development only)
npm run db:reset
```

### Docker Operations
```bash
# View logs
docker-compose logs -f

# Restart services
docker-compose restart

# Stop everything
docker-compose down
```

## 📚 Next Steps

1. **Read the Documentation**:
   - [Developer Manual](docs/DEVELOPER_MANUAL.md) - Complete development guide
   - [User Manual](docs/USER_MANUAL.md) - End-user documentation
   - [API Reference](docs/API_REFERENCE.md) - Backend API documentation

2. **Customize Your App**:
   - Update branding in `frontend/src/components/`
   - Modify matching algorithm in `backend/src/services/matchService.js`
   - Add new features following the module structure

3. **Deploy to Production**:
   - Follow the [Deployment Guide](docs/DEPLOYMENT.md)
   - Set up monitoring and backups
   - Configure SSL certificates

## 🛠️ Common Tasks

### Adding a New Feature

1. **Frontend Component**:
```bash
# Create component
touch frontend/src/components/feature/NewFeature.jsx

# Add to pages if needed
touch frontend/src/pages/NewFeaturePage.jsx

# Create service
touch frontend/src/services/newFeatureService.js
```

2. **Backend API**:
```bash
# Create controller
touch backend/src/controllers/newFeatureController.js

# Create service
touch backend/src/services/newFeatureService.js

# Create routes
touch backend/src/routes/newFeature.js
```

3. **Database Changes**:
```bash
# Create migration
cd backend
npx prisma migrate dev --name add_new_feature
```

### Customizing the Matching Algorithm

Edit `backend/src/services/matchService.js`:
```javascript
// Modify the calculateMatchScore function
calculateMatchScore(user1, user2) {
  // Your custom matching logic here
  return score;
}
```

### Adding New Photo Tags

Update `frontend/src/utils/constants.js`:
```javascript
export const PHOTO_TAGS = [
  'nature', 'sunset', 'travel', 'art',
  'your-new-tag' // Add your tags here
];
```

## 🔍 Troubleshooting

### Common Issues

**Database Connection Error**:
```bash
# Check if PostgreSQL is running
docker-compose ps database

# View database logs
docker-compose logs database
```

**Frontend Not Loading**:
```bash
# Check if all services are running
docker-compose ps

# Restart frontend
docker-compose restart frontend
```

**API Errors**:
```bash
# Check backend logs
docker-compose logs backend

# Verify environment variables
cat .env
```

### Getting Help

1. **Check the logs**:
   ```bash
   docker-compose logs -f
   ```

2. **Verify configuration**:
   ```bash
   # Check environment variables
   cat .env
   
   # Test database connection
   npm run db:test
   ```

3. **Reset everything** (if needed):
   ```bash
   docker-compose down -v
   docker-compose up -d
   npm run db:setup
   ```

## 📞 Support

- **Documentation**: Check the `docs/` folder
- **Issues**: Create an issue in the repository
- **Email**: <EMAIL>

## 🎉 You're Ready!

Congratulations! You now have a fully functional photo-based dating platform. Start by:

1. Creating a test account
2. Uploading some photos
3. Testing the swipe functionality
4. Exploring the admin panel
5. Customizing the features to your needs

Happy coding! 🚀

---

**Quick Start Version**: 1.0.0  
**Estimated Setup Time**: 5-10 minutes  
**Last Updated**: December 2024
