# SoloyLibre - Photo-Based Dating Platform

## 🌟 Project Overview

SoloyLibre is a modern, photo-centric dating platform that connects people through shared visual interests. Users discover potential matches by interacting with photos (landscapes, art, animals, etc.) rather than traditional profile browsing.

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React with Vite (modern, fast development)
- **Backend**: Node.js with Express
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage
- **Containerization**: Docker & Docker Compose
- **Deployment**: Docker containers with nginx reverse proxy

### Project Structure
```
soloylibre-app/
├── frontend/                 # React frontend application
├── backend/                  # Node.js API server
├── database/                 # Database schemas and migrations
├── docker/                   # Docker configurations
├── docs/                     # Documentation and manuals
├── scripts/                  # Deployment and utility scripts
├── docker-compose.yml        # Multi-container orchestration
└── README.md                 # This file
```

## 🚀 Quick Start

### Prerequisites
- <PERSON>er and Docker Compose
- Node.js 18+ (for local development)
- Git

### Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd soloylibre-app

# Start the entire stack
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# Database: localhost:5432
```

## 📚 Documentation

- [Developer Manual](docs/DEVELOPER_MANUAL.md) - Complete development guide
- [User Manual](docs/USER_MANUAL.md) - End-user documentation
- [API Documentation](docs/API_REFERENCE.md) - Backend API reference
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment
- [Security Guide](docs/SECURITY.md) - Security best practices

## 🔧 Development

### Local Development Setup
```bash
# Install dependencies
npm run install:all

# Start development servers
npm run dev

# Run tests
npm run test

# Build for production
npm run build
```

### Available Scripts
- `npm run dev` - Start development servers
- `npm run build` - Build production bundles
- `npm run test` - Run test suites
- `npm run lint` - Code linting
- `npm run format` - Code formatting
- `npm run backup` - Create system backup
- `npm run restore` - Restore from backup

## 🛡️ Security Features

- HTTPS encryption for all communications
- JWT-based authentication with refresh tokens
- CAPTCHA protection on registration/login
- Rate limiting and brute-force protection
- Input validation and sanitization
- SQL injection prevention
- XSS and CSRF protection
- Comprehensive audit logging

## 📊 Core Features

### Photo Discovery System
- Rotating photo gallery on homepage
- Swipe-based interaction (like/dislike)
- Tag-based filtering and search
- Smart matching algorithm

### User Interaction
- Profile creation and management
- Photo uploading with tagging
- Commenting and emoji reactions
- Private messaging system
- Match notifications

### Admin Panel
- User and content management
- Analytics dashboard
- Moderation tools
- Security monitoring
- Backup management

## 🔄 Backup & Recovery

### Automated Backups
- Daily database backups
- File storage synchronization
- Configuration backup
- Docker image versioning

### Recovery Process
1. Stop running containers
2. Restore database from backup
3. Restore file storage
4. Apply configuration files
5. Restart services
6. Verify system integrity

## 📈 Monitoring & Analytics

- User engagement metrics
- Photo interaction statistics
- Match success rates
- Security incident tracking
- Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📄 License

[License information to be added]

## 🆘 Support

For technical support or questions:
- Check the [Developer Manual](docs/DEVELOPER_MANUAL.md)
- Review [API Documentation](docs/API_REFERENCE.md)
- Create an issue in the repository

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Maintainer**: Development Team
